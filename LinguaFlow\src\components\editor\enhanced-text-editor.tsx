"use client";

import React, { useState, useRef, useCallback, useEffect, forwardRef, useMemo, type ChangeEvent, type UIEvent } from 'react';
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FileText, IterationCw, Loader2, Eraser, Copy, Check, Bold, Italic, Underline, AlignLeft, AlignCenter, AlignRight, Undo2, Redo2, Wand2, List, ListOrdered, ShieldCheck } from "lucide-react";
import { rephraseText, type RephraseTextInput } from '@/ai/flows/contextual-ai-rephraser';
import { type AnalysisSuggestion } from '@/ai/flows/text-analysis-flow';
import { useToast } from '@/hooks/use-toast';
import { Label } from '../ui/label';
import { useI18n } from '@/contexts/i18n-context';
import { cn } from '@/lib/utils';
import { Separator } from '../ui/separator';
import { WordToolkitPopover } from './word-toolkit-popover';
import { useDebounce } from '@/hooks/use-debounce';

export interface SelectionDetail {
  text: string;
  start: number;
  end: number;
}

export type PlagiarismSource = Omit<AnalysisSuggestion, 'type'> & { type: 'plagiarism' };
export type RephraseSource = Omit<AnalysisSuggestion, 'type'> & { type: 'rephrase' };

type CombinedSuggestion = AnalysisSuggestion | PlagiarismSource;

interface EnhancedTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  writingMode: string;
  direction: 'ltr' | 'rtl';
  suggestions: AnalysisSuggestion[];
  plagiarismSources: PlagiarismSource[];
  onApplySuggestion: (suggestionText: string, originalSegment: string, startIndex?: number, endIndex?: number) => void;
  onDismissSuggestion: (suggestionId: string) => void;
  onUndo: () => void;
  onRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
}

interface SuggestionPopoverProps {
    suggestion: AnalysisSuggestion, 
    onApply: (s: AnalysisSuggestion) => void,
    onDismiss: (id: string) => void,
    onClose: () => void;
}

interface PlagiarismPopoverProps {
    source: PlagiarismSource, 
    onClose: () => void;
}

const fontOptions = [
    { value: "'Inter', sans-serif", label: 'Inter' },
    { value: "'Source Code Pro', monospace", label: 'Source Code Pro' },
    { value: "'Georgia', serif", label: 'Georgia' },
    { value: "'Times New Roman', Times, serif", label: 'Times New Roman' },
];


// A single popover for inline suggestions
function SuggestionPopover({ 
    suggestion, 
    onApply,
    onDismiss,
    onClose,
}: SuggestionPopoverProps) {
    const { t } = useI18n();

    const handleApply = () => {
        onApply(suggestion);
        onClose();
    };

    const handleDismiss = () => {
        onDismiss(suggestion.id);
        onClose();
    };

    return (
        <PopoverContent className="w-auto max-w-sm p-3 shadow-xl" side="top" align="start" onEscapeKeyDown={onClose}>
            <div className="space-y-3">
                <p className="text-sm text-muted-foreground">{suggestion.message}</p>
                <div className="p-2 rounded-md bg-muted border">
                    <p className="text-sm text-destructive line-through">{suggestion.originalSegment}</p>
                    <p className="text-sm text-primary font-semibold">{suggestion.suggestion}</p>
                </div>
                <div className="flex justify-end gap-2">
                    <Button size="sm" variant="ghost" onClick={handleDismiss}>
                        {t('dismissButton')}
                    </Button>
                    <Button size="sm" onClick={handleApply}>
                        <Check className="mr-2 h-4 w-4" />
                        {t('correctButton')}
                    </Button>
                </div>
            </div>
        </PopoverContent>
    );
}

function PlagiarismPopover({ source, onClose }: PlagiarismPopoverProps) {
    const { t } = useI18n();

    return (
        <PopoverContent className="w-auto max-w-sm p-3 shadow-xl" side="top" align="start" onEscapeKeyDown={onClose}>
            <div className="space-y-3">
                <div className="flex items-center gap-2 font-semibold text-destructive">
                    <ShieldCheck className="h-5 w-5" />
                    {t('plagiarismReportLabel')}
                </div>
                <p className="text-sm text-muted-foreground">{source.message}</p>
                <div className="p-2 rounded-md bg-destructive/10 border border-destructive/20">
                    <p className="text-sm text-destructive">
                        <span className="font-semibold">{t('originalSourceLabel')}:</span>{' '}
                        <a href={source.suggestion} target="_blank" rel="noopener noreferrer" className="underline break-all">
                            {source.suggestion}
                        </a>
                    </p>
                </div>
                <div className="flex justify-end">
                    <Button size="sm" variant="ghost" onClick={onClose}>
                        {t('dismissButton')}
                    </Button>
                </div>
            </div>
        </PopoverContent>
    );
}


export const EnhancedTextEditor = forwardRef<HTMLDivElement, EnhancedTextEditorProps>(
  ({ value, onChange, writingMode, direction, suggestions, plagiarismSources, onApplySuggestion, onDismissSuggestion, onUndo, onRedo, canUndo, canRedo }, ref) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const backdropRef = useRef<HTMLDivElement>(null);
  const { t, getWritingLanguageBase } = useI18n();
  const { toast } = useToast();
  
  const [selectedTextDetail, setSelectedTextDetail] = useState<SelectionDetail | null>(null);
  const debouncedSelection = useDebounce(selectedTextDetail, 300);

  const [isAiPopoverOpen, setIsAiPopoverOpen] = useState(false);
  const [rephrasedText, setRephrasedText] = useState<string>("");
  const [isRephrasing, setIsRephrasing] = useState(false);
  const [activeSuggestion, setActiveSuggestion] = useState<CombinedSuggestion | null>(null);
  const [fontFamily, setFontFamily] = useState(fontOptions[0].value);
  
  const isSingleWordSelection = useMemo(() => 
      debouncedSelection ? !debouncedSelection.text.trim().includes(' ') && debouncedSelection.text.trim().length > 0 : false
  , [debouncedSelection]);


  const placeholder = t('startWritingPlaceholder');
  const handleChange = (event: ChangeEvent<HTMLTextAreaElement>) => {
    onChange(event.target.value);
  };
  
  const handleScroll = (event: UIEvent<HTMLTextAreaElement>) => {
    if (backdropRef.current) {
        backdropRef.current.scrollTop = event.currentTarget.scrollTop;
        backdropRef.current.scrollLeft = event.currentTarget.scrollLeft;
    }
  };

  const handleSelect = () => {
    if (textareaRef.current) {
      const { selectionStart, selectionEnd, value: editorValue } = textareaRef.current;
      const selectedText = editorValue.substring(selectionStart, selectionEnd);

      if (selectionStart !== selectionEnd && selectedText.trim().length > 0) {
        setSelectedTextDetail({ text: selectedText, start: selectionStart, end: selectionEnd });
      } else {
        setSelectedTextDetail(null);
        setIsAiPopoverOpen(false);
      }
    }
  };

  const handleRephrase = useCallback(async () => {
    if (!debouncedSelection || !debouncedSelection.text.trim() || isSingleWordSelection) {
      return;
    }
    setIsRephrasing(true);
    setRephrasedText("");
    try {
      const input: RephraseTextInput = {
        selectedText: debouncedSelection.text,
        contextText: value,
        tone: writingMode,
        style: writingMode,
      };
      const result = await rephraseText(input);
      if (!result || typeof result.rephrasedText === 'undefined') {
        throw new Error('AI model did not return the expected output format.');
      }
      setRephrasedText(result.rephrasedText);
      toast({ titleKey: "toastSuggestionReady", descriptionKey: "toastTextGeneratedSuccess" });
    } catch (error) {
      console.error("Error rephrasing text:", error);
      toast({ titleKey: "toastErrorTitle", descriptionKey: "toastRephraseError", variant: "destructive" });
      setIsAiPopoverOpen(false);
    } finally {
      setIsRephrasing(false);
    }
  }, [debouncedSelection, value, writingMode, toast, isSingleWordSelection]);
  
  useEffect(() => {
    if (isAiPopoverOpen && debouncedSelection && !isSingleWordSelection && !rephrasedText && !isRephrasing) {
        handleRephrase();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAiPopoverOpen, debouncedSelection, rephrasedText, isRephrasing, handleRephrase, isSingleWordSelection]);


  const applyRephrasedText = () => {
    if (debouncedSelection && rephrasedText) {
      const newValue =
        value.substring(0, debouncedSelection.start) +
        rephrasedText +
        value.substring(debouncedSelection.end);
      onChange(newValue); 
      setIsAiPopoverOpen(false);
      setSelectedTextDetail(null);
      setRephrasedText("");
      if (textareaRef.current) {
        textareaRef.current.focus();
        const newCursorPosition = debouncedSelection.start + rephrasedText.length;
        setTimeout(() => {
          if (textareaRef.current) {
            textareaRef.current.setSelectionRange(newCursorPosition, newCursorPosition);
          }
        }, 0);
      }
    }
  };
  
  const handleApplyInlineSuggestion = (suggestion: AnalysisSuggestion) => {
    onApplySuggestion(suggestion.suggestion, suggestion.originalSegment, suggestion.startIndex, suggestion.endIndex);
  };
  
  const handleApplyWordReplacement = (replacement: string) => {
    if (debouncedSelection) {
      const newValue =
        value.substring(0, debouncedSelection.start) +
        replacement +
        value.substring(debouncedSelection.end);
      onChange(newValue);
      
      toast({ titleKey: "toastSuccessTitle", descriptionKey: "toastWordReplacedSuccess", descriptionParams: { word: replacement } });

      setIsAiPopoverOpen(false);
      setSelectedTextDetail(null);
      
      if (textareaRef.current) {
        textareaRef.current.focus();
        const newCursorPosition = debouncedSelection.start + replacement.length;
        setTimeout(() => {
          if(textareaRef.current) {
            textareaRef.current.setSelectionRange(newCursorPosition, newCursorPosition);
          }
        }, 0);
      }
    }
  };

  const handleFormat = (formatType: 'bold' | 'italic' | 'underline' | 'ordered-list' | 'unordered-list') => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    let { selectionStart, selectionEnd } = textarea;

    // For lists, find the start and end of the line(s)
    if (formatType === 'ordered-list' || formatType === 'unordered-list') {
      let lineStartIndex = value.lastIndexOf('\n', selectionStart - 1) + 1;
      
      // If no text is selected, operate on the current line
      if (selectionStart === selectionEnd) {
          selectionEnd = value.indexOf('\n', selectionStart);
          if (selectionEnd === -1) {
              selectionEnd = value.length;
          }
      }

      let lineEndIndex = value.indexOf('\n', selectionEnd - 1);
      if (lineEndIndex === -1 || lineEndIndex < selectionStart) {
        lineEndIndex = value.length;
      }
      
      const textToFormat = value.substring(lineStartIndex, lineEndIndex);
      const lines = textToFormat.split('\n');
      let formattedLines;
      let isList = false;

      if (formatType === 'ordered-list') {
          isList = lines.every(line => /^\s*\d+\.\s/.test(line.trim()) || line.trim() === '');
          if (isList) {
              formattedLines = lines.map(line => line.replace(/^\s*\d+\.\s*/, ''));
          } else {
              let counter = 1;
              formattedLines = lines.map(line => {
                  const trimmedLine = line.trim().replace(/^•\s/, '');
                  return trimmedLine ? `${counter++}. ${trimmedLine}` : '';
              });
          }
      } else { // unordered-list
          isList = lines.every(line => /^\s*•\s/.test(line.trim()) || line.trim() === '');
          if (isList) {
              formattedLines = lines.map(line => line.replace(/^\s*•\s*/, ''));
          } else {
              formattedLines = lines.map(line => {
                  const trimmedLine = line.trim().replace(/^\d+\.\s/, '');
                  return trimmedLine ? `• ${trimmedLine}` : '';
              });
          }
      }

      const formattedBlock = formattedLines.join('\n');
      const newValue = value.substring(0, lineStartIndex) + formattedBlock + value.substring(lineEndIndex);
      onChange(newValue);

      setTimeout(() => {
          textarea.focus();
          textarea.setSelectionRange(lineStartIndex, lineStartIndex + formattedBlock.length);
      }, 0);

    } else { // Bold, Italic, Underline
      const selectedText = value.substring(selectionStart, selectionEnd);
      if (!selectedText) return;
      
      let formattedText;
      switch (formatType) {
        case 'bold': formattedText = `**${selectedText}**`; break;
        case 'italic': formattedText = `*${selectedText}*`; break;
        case 'underline': formattedText = `<u>${selectedText}</u>`; break;
        default: return;
      }

      const newValue = value.substring(0, selectionStart) + formattedText + value.substring(selectionEnd);
      onChange(newValue);

      setTimeout(() => {
          textarea.focus();
          const newCursorPos = selectionStart + formattedText.length;
          textarea.setSelectionRange(newCursorPos, newCursorPos);
      }, 0);
    }
  };
  
  const renderFormattedText = (text: string): (string | JSX.Element)[] => {
    if (!text) return [];
    
    let parts: (string | JSX.Element)[] = [text];

    const processRegex = (regex: RegExp, tag: 'strong' | 'em' | 'u', keyPrefix: string) => {
        parts = parts.flatMap((part, partIndex) => {
            if (typeof part !== 'string') return part;

            const splitParts = part.split(regex);
            const processed: (string | JSX.Element)[] = [];

            for (let i = 0; i < splitParts.length; i++) {
                if (i % 2 === 0) {
                    if(splitParts[i]) processed.push(splitParts[i]);
                } else {
                    const Tag = tag;
                    processed.push(<Tag key={`${keyPrefix}-${partIndex}-${i}`}>{splitParts[i]}</Tag>);
                }
            }
            return processed;
        });
    };
    
    processRegex(/\*\*([\s\S]*?)\*\*/g, 'strong', 'bold');
    processRegex(/\*([\s\S]*?)\*/g, 'em', 'italic');
    processRegex(/<u>([\s\S]*?)<\/u>/g, 'u', 'underline');

    return parts;
};

  const combinedSuggestions = useMemo(() => {
    const allSuggestions = [...suggestions, ...plagiarismSources] as CombinedSuggestion[];
    return allSuggestions.sort((a, b) => (a.startIndex || 0) - (b.startIndex || 0));
  }, [suggestions, plagiarismSources]);
  
  const handleHighlightClick = (suggestion: CombinedSuggestion) => {
      setActiveSuggestion(suggestion);
  };
  const closeSuggestionPopover = () => {
      setActiveSuggestion(null);
  }

  const highlightedContent = useMemo(() => {
    if (!value) return <p>&nbsp;</p>; // Render a non-breaking space to maintain height
    
    const sortedSuggestions = combinedSuggestions;

    let lastIndex = 0;
    const elements = [];

    sortedSuggestions.forEach((suggestion) => {
        const { startIndex, endIndex, originalSegment, id, type } = suggestion;

        if (startIndex === undefined || endIndex === undefined || startIndex < lastIndex) {
            return; 
        }

        // Add the text before the current suggestion
        if (startIndex > lastIndex) {
            elements.push(<React.Fragment key={`text-${lastIndex}`}>{renderFormattedText(value.slice(lastIndex, startIndex))}</React.Fragment>);
        }

        const highlightClass = cn('suggestion-highlight', {
            'suggestion-spelling': type === 'spelling',
            'suggestion-grammar': type === 'grammar',
            'suggestion-rewrite': type === 'rewrite',
            'suggestion-style': type === 'style',
            'suggestion-plagiarism': type === 'plagiarism'
        });

        elements.push(
            <Popover key={id} open={activeSuggestion?.id === id} onOpenChange={(open) => {if (!open) closeSuggestionPopover()}}>
                <PopoverTrigger asChild>
                    <span className={highlightClass} onClick={() => handleHighlightClick(suggestion)}>
                        {renderFormattedText(originalSegment)}
                    </span>
                </PopoverTrigger>
                {activeSuggestion?.id === id && (
                    type === 'plagiarism' ? (
                        <PlagiarismPopover 
                            source={suggestion as PlagiarismSource}
                            onClose={closeSuggestionPopover}
                        />
                    ) : (
                        <SuggestionPopover 
                            suggestion={suggestion as AnalysisSuggestion}
                            onApply={handleApplyInlineSuggestion} 
                            onDismiss={onDismissSuggestion}
                            onClose={closeSuggestionPopover}
                        />
                    )
                )}
            </Popover>
        );

        lastIndex = endIndex;
    });

    // Add any remaining text after the last suggestion
    if (lastIndex < value.length) {
        elements.push(<React.Fragment key={`text-${lastIndex}`}>{renderFormattedText(value.slice(lastIndex))}</React.Fragment>);
    }

    // Split the entire content by newlines to create <p> tags for each line
    const finalElements = [];
    let lineBuffer: (string|JSX.Element)[] = [];

    elements.forEach((el, index) => {
        if (typeof el.props.children === 'string') {
            const text = el.props.children;
            const lines = text.split('\n');
            lines.forEach((line: string, lineIndex: number) => {
                if(line) lineBuffer.push(line);
                if (lineIndex < lines.length - 1) {
                    finalElements.push(<p key={`line-${finalElements.length}`}>{lineBuffer.length > 0 ? lineBuffer : <>&nbsp;</>}</p>);
                    lineBuffer = [];
                }
            });
        } else {
            lineBuffer.push(el);
        }
    });

    if (lineBuffer.length > 0) {
        finalElements.push(<p key={`line-${finalElements.length}`}>{lineBuffer}</p>);
    }
    
    if (finalElements.length === 0 && value === "") {
      return <p>&nbsp;</p>;
    }

    return <>{finalElements}</>;
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value, combinedSuggestions, onDismissSuggestion, activeSuggestion]);

  const handleClearText = () => {
    onChange("");
    toast({ titleKey: "toastSuccessTitle", descriptionKey: "toastEditorClearedSuccess" });
  };

  const handleCopyText = async () => {
    if (!value) {
      toast({ titleKey: "toastErrorTitle", descriptionKey: "toastEditorEmptyForCopyError", variant: "destructive" });
      return;
    }
    try {
      await navigator.clipboard.writeText(value);
      toast({ titleKey: "toastSuccessTitle", descriptionKey: "toastEditorContentCopiedSuccess" });
    } catch (error) {
      console.error("Failed to copy text from editor:", error);
      toast({ titleKey: "toastErrorTitle", descriptionKey: "toastEditorContentCopyError", variant: "destructive" });
    }
  };
  
  const editorStyles: React.CSSProperties = {
    fontFamily,
    fontSize: 'inherit',
    lineHeight: 'inherit',
    letterSpacing: 'inherit',
    padding: '1rem',
    boxSizing: 'border-box',
    width: '100%',
    height: '100%',
    resize: 'none',
    border: 'none',
    outline: 'none',
    whiteSpace: 'pre-wrap',
    wordWrap: 'break-word',
  };

  return (
    <Card className="h-full flex flex-col" ref={ref}>
      <CardHeader className="p-4 border-b flex flex-row items-center justify-between gap-4 flex-wrap">
        <CardTitle className="flex items-center text-lg shrink-0">
          <FileText className="mr-2 h-4 w-4 text-primary" />
          {t('editorTitle')}
        </CardTitle>
        <div className="flex items-center flex-nowrap gap-x-2 justify-end grow">
            <div className="flex items-center gap-1">
                <Button variant="outline" size="icon" className="h-8 w-8" onClick={onUndo} disabled={!canUndo} title={t('undoButton')}>
                    <Undo2 className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="icon" className="h-8 w-8" onClick={onRedo} disabled={!canRedo} title={t('redoButton')}>
                    <Redo2 className="h-4 w-4" />
                </Button>
            </div>

            <Separator orientation="vertical" className="h-6" />

            <Select value={fontFamily} onValueChange={setFontFamily}>
                <SelectTrigger className="w-[140px] h-8">
                    <SelectValue placeholder="Select font" />
                </SelectTrigger>
                <SelectContent>
                    {fontOptions.map(font => (
                        <SelectItem key={font.value} value={font.value} style={{fontFamily: font.value}}>
                            {font.label}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>

            <Separator orientation="vertical" className="h-6" />

            <div className="flex items-center gap-0.5">
                <Button variant="outline" size="icon" className="h-8 w-8" onClick={() => handleFormat('bold')} title="Bold">
                    <Bold className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="icon" className="h-8 w-8" onClick={() => handleFormat('italic')} title="Italic">
                    <Italic className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="icon" className="h-8 w-8" onClick={() => handleFormat('underline')} title="Underline">
                    <Underline className="h-4 w-4" />
                </Button>
                 <Button variant="outline" size="icon" className="h-8 w-8" onClick={() => handleFormat('ordered-list')} title="Numbered List">
                    <ListOrdered className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="icon" className="h-8 w-8" onClick={() => handleFormat('unordered-list')} title="Bulleted List">
                    <List className="h-4 w-4" />
                </Button>
            </div>
            
            <div className="flex items-center gap-0.5">
              <Button variant="outline" size="icon" className="h-8 w-8" onClick={() => {}} title="Align Left">
                  <AlignLeft className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon" className="h-8 w-8" onClick={() => {}} title="Align Center">
                  <AlignCenter className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon" className="h-8 w-8" onClick={() => {}} title="Align Right">
                  <AlignRight className="h-4 w-4" />
              </Button>
            </div>
        </div>
      </CardHeader>
      <CardContent className="flex-grow p-0 relative">
          <div className="grid h-full">
            <div 
              ref={backdropRef} 
              className="z-[1] col-start-1 row-start-1 overflow-auto pointer-events-none absolute inset-0"
              style={{...editorStyles, textAlign: direction === 'rtl' ? 'right' : 'left'}}
              dir={direction}
            >
              {highlightedContent}
            </div>
            <Textarea
              ref={textareaRef}
              value={value}
              onChange={handleChange}
              onSelect={handleSelect}
              onScroll={handleScroll}
              placeholder={placeholder}
              className="relative z-0 col-start-1 row-start-1 rounded-none focus-visible:ring-0 focus-visible:ring-offset-0 min-h-[250px] max-h-[70vh] bg-transparent"
              style={{...editorStyles, color: 'transparent', caretColor: 'hsl(var(--foreground))', textAlign: direction === 'rtl' ? 'right' : 'left'}}
              aria-label={t('editorTitle')}
              dir={direction}
            />
          </div>
      </CardContent>
      <CardFooter className="p-4 border-t flex items-center justify-start gap-2 flex-wrap">
        <Popover open={isAiPopoverOpen} onOpenChange={setIsAiPopoverOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              disabled={!debouncedSelection || isRephrasing}
            >
              <Wand2 className="mr-2 h-4 w-4" />
              {t('aiToolsButton')}
            </Button>
          </PopoverTrigger>
          {debouncedSelection && (
            isSingleWordSelection ? (
                <WordToolkitPopover 
                    selectedWord={debouncedSelection.text}
                    contextText={value}
                    language={getWritingLanguageBase()}
                    onSynonymSelect={handleApplyWordReplacement}
                />
            ) : (
                <PopoverContent className="w-80" side="top" align="start">
                <div className="grid gap-4">
                  <div className="space-y-2">
                    <h4 className="font-medium leading-none">{t('rephrasePopoverTitle')}</h4>
                    <p className="text-sm text-muted-foreground">
                      {t('rephrasePopoverDescription')}
                    </p>
                  </div>
                  
                    <div className="space-y-1">
                      <Label htmlFor="original-text-popover">{t('originalTextLabel')}</Label>
                      <Textarea
                        id="original-text-popover"
                        value={debouncedSelection.text}
                        readOnly
                        className="h-20 text-xs bg-muted/50"
                      />
                    </div>
                  
                  {isRephrasing && (
                    <div className="flex items-center justify-center h-20">
                      <Loader2 className="h-5 w-5 animate-spin text-primary" />
                    </div>
                  )}
                  {!isRephrasing && rephrasedText && (
                     <div className="space-y-1">
                      <Label htmlFor="rephrased-text-popover">{t('suggestionTextLabel')}</Label>
                      <Textarea
                        id="rephrased-text-popover"
                        value={rephrasedText}
                        readOnly
                        className="h-20 text-xs bg-muted"
                      />
                    </div>
                  )}
                  <div className="flex justify-end gap-2">
                    <Button variant="ghost" size="sm" onClick={() => setIsAiPopoverOpen(false)}>{t('cancelButton')}</Button>
                    <Button
                      size="sm"
                      onClick={applyRephrasedText}
                      disabled={isRephrasing || !rephrasedText}
                    >
                      {t('applyButton')}
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            )
          )}
        </Popover>

        <Button
          variant="outline"
          size="sm"
          onClick={handleClearText}
          disabled={value.length === 0}
          aria-label={t('clearEditorButtonAriaLabel')}
          title={t('clearEditorButtonAriaLabel')}
        >
          <Eraser className="mr-2 h-4 w-4" />
          {t('clearEditorButton')}
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={handleCopyText}
          disabled={value.length === 0}
          aria-label={t('copyEditorButtonAriaLabel')}
          title={t('copyEditorButtonAriaLabel')}
        >
          <Copy className="mr-2 h-4 w-4" />
          {t('copyEditorButton')}
        </Button>
      </CardFooter>
    </Card>
  );
});

EnhancedTextEditor.displayName = "EnhancedTextEditor";
