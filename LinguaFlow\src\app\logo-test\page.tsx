"use client";

import { CustomLogo } from '@/components/icons/custom-logo';

export default function LogoTestPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8 text-gray-800 dark:text-white">
          LinguaFlow Logo Test Page
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          
          {/* Different Sizes */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-700 dark:text-gray-200">
              Different Sizes
            </h2>
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <CustomLogo size={16} />
                <span className="text-sm text-gray-600 dark:text-gray-400">16px (favicon size)</span>
              </div>
              <div className="flex items-center gap-4">
                <CustomLogo size={32} />
                <span className="text-sm text-gray-600 dark:text-gray-400">32px (small)</span>
              </div>
              <div className="flex items-center gap-4">
                <CustomLogo size={48} />
                <span className="text-sm text-gray-600 dark:text-gray-400">48px (medium)</span>
              </div>
              <div className="flex items-center gap-4">
                <CustomLogo size={64} />
                <span className="text-sm text-gray-600 dark:text-gray-400">64px (large)</span>
              </div>
            </div>
          </div>

          {/* Light/Dark Variants */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-700 dark:text-gray-200">
              Theme Variants
            </h2>
            <div className="space-y-4">
              <div className="bg-white p-4 rounded border">
                <CustomLogo variant="light" size={48} />
                <p className="text-sm text-gray-600 mt-2">Light theme</p>
              </div>
              <div className="bg-gray-900 p-4 rounded border">
                <CustomLogo variant="dark" size={48} />
                <p className="text-sm text-gray-300 mt-2">Dark theme</p>
              </div>
            </div>
          </div>

          {/* Responsive Behavior */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-700 dark:text-gray-200">
              Responsive
            </h2>
            <div className="space-y-4">
              <CustomLogo className="w-8 h-8" />
              <CustomLogo className="w-12 h-12" />
              <CustomLogo className="w-16 h-16" />
              <CustomLogo className="w-20 h-20" />
            </div>
          </div>

          {/* Color Backgrounds */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-700 dark:text-gray-200">
              Different Backgrounds
            </h2>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-blue-500 p-4 rounded flex justify-center">
                <CustomLogo size={40} />
              </div>
              <div className="bg-gray-200 p-4 rounded flex justify-center">
                <CustomLogo size={40} />
              </div>
              <div className="bg-gray-800 p-4 rounded flex justify-center">
                <CustomLogo size={40} />
              </div>
              <div className="bg-green-500 p-4 rounded flex justify-center">
                <CustomLogo size={40} />
              </div>
            </div>
          </div>

          {/* Animation Test */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-700 dark:text-gray-200">
              Hover Animation
            </h2>
            <div className="flex justify-center">
              <CustomLogo 
                size={64} 
                className="transition-transform duration-300 hover:scale-110 cursor-pointer" 
              />
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 text-center mt-2">
              Hover to see animation
            </p>
          </div>

          {/* Implementation Status */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-700 dark:text-gray-200">
              Implementation Status
            </h2>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <span className="w-3 h-3 bg-yellow-500 rounded-full"></span>
                <span>Logo component ready</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-3 h-3 bg-red-500 rounded-full"></span>
                <span>SVG content needs replacement</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-3 h-3 bg-red-500 rounded-full"></span>
                <span>Favicon files needed</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-3 h-3 bg-green-500 rounded-full"></span>
                <span>Manifest.json created</span>
              </div>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-blue-800 dark:text-blue-200">
            Next Steps
          </h2>
          <ol className="list-decimal list-inside space-y-2 text-blue-700 dark:text-blue-300">
            <li>Convert your logo image to SVG using vectorizer.io</li>
            <li>Replace the SVG content in src/components/icons/custom-logo.tsx</li>
            <li>Generate favicon files using favicon.io</li>
            <li>Place favicon files in the correct directories</li>
            <li>Test this page again to verify everything works</li>
          </ol>
        </div>

        {/* Quick Links */}
        <div className="mt-6 text-center space-x-4">
          <a 
            href="https://vectorizer.io" 
            target="_blank" 
            rel="noopener noreferrer"
            className="inline-block bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors"
          >
            Convert to SVG
          </a>
          <a 
            href="https://favicon.io" 
            target="_blank" 
            rel="noopener noreferrer"
            className="inline-block bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded transition-colors"
          >
            Generate Favicons
          </a>
          <a 
            href="/" 
            className="inline-block bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors"
          >
            Back to App
          </a>
        </div>
      </div>
    </div>
  );
}
