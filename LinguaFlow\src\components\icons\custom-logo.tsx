import type { SVGProps } from 'react';

interface CustomLogoProps extends SVGProps<SVGSVGElement> {
  variant?: 'light' | 'dark';
  size?: number;
}

export function CustomLogo({ variant = 'light', size, ...props }: CustomLogoProps) {
  return (
    <svg
      width={size || "100"}
      height={size || "100"}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      {/*
        INSTRUCTIONS FOR REPLACING WITH YOUR LOGO:

        1. Convert your logo image to SVG using:
           - vectorizer.io (recommended)
           - convertio.co
           - Adobe Illustrator
           - Inkscape (free)

        2. Copy the SVG content (everything between <svg> tags)

        3. Replace everything between these comments with your SVG paths

        4. Make sure to:
           - Update viewBox to match your logo's dimensions
           - Add unique IDs to gradients/defs (prefix with "linguaflow-")
           - Consider the variant prop for light/dark themes

        5. Your logo should have these colors based on your image:
           - Main blue: #5DADE2 or similar
           - White elements for contrast
           - Consider adding theme-aware colors
      */}

      {/* TEMPORARY PLACEHOLDER - Replace with your logo SVG */}
      <defs>
        <linearGradient
          id="linguaflow-gradient-1"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#5DADE2" />
          <stop offset="1" stopColor="#3498DB" />
        </linearGradient>
      </defs>

      {/* Circular background */}
      <circle
        cx="50"
        cy="50"
        r="48"
        fill="url(#linguaflow-gradient-1)"
        stroke={variant === 'dark' ? '#ffffff' : '#e5e7eb'}
        strokeWidth="2"
      />

      {/* Placeholder for your logo elements */}
      <text
        x="50"
        y="55"
        textAnchor="middle"
        fill="white"
        fontSize="12"
        fontFamily="Inter, sans-serif"
        fontWeight="600"
      >
        LOGO
      </text>

      {/* END PLACEHOLDER */}
    </svg>
  );
}
