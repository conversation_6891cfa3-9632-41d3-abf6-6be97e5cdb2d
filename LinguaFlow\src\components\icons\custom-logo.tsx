import type { SVGProps } from 'react';

export function CustomLogo(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="100"
      height="100"
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      {/* Replace this entire SVG content with your converted logo SVG */}
      {/* For now, keeping the original structure as a template */}
      <defs>
        <linearGradient
          id="logoFeatherGradient"
          x1="50"
          y1="25"
          x2="50"
          y2="75"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#81c7f5" />
          <stop offset="1" stopColor="#3b82f6" />
        </linearGradient>
        <linearGradient
          id="logoLGradient"
          x1="30"
          y1="25"
          x2="70"
          y2="75"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#a5d8f8" />
          <stop offset="1" stopColor="#60a5fa" />
        </linearGradient>
      </defs>
      <circle cx="50" cy="50" r="50" fill="white" />
      <path
        d="M32 25 L32 75 L60 75"
        stroke="url(#logoLGradient)"
        strokeWidth="11"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
      />
      <path
        d="M48 25 C 55 30, 75 35, 75 50 C 75 65, 55 70, 48 75"
        fill="url(#logoFeatherGradient)"
      />
      <path
        d="M57 40 L70 40 M57 50 L75 50 M57 60 L70 60"
        stroke="white"
        strokeWidth="3.5"
        strokeLinecap="round"
      />
    </svg>
  );
}
