(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_c7f26f5b._.js", {

"[project]/src/hooks/use-toast.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "reducer": (()=>reducer),
    "toast": (()=>toast),
    "useToast": (()=>useToast)
});
// Inspired by react-hot-toast library
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
"use client";
;
const TOAST_LIMIT = 1;
const TOAST_REMOVE_DELAY = 5000;
const actionTypes = {
    ADD_TOAST: "ADD_TOAST",
    UPDATE_TOAST: "UPDATE_TOAST",
    DISMISS_TOAST: "DISMISS_TOAST",
    REMOVE_TOAST: "REMOVE_TOAST"
};
let count = 0;
function genId() {
    count = (count + 1) % Number.MAX_SAFE_INTEGER;
    return count.toString();
}
const toastTimeouts = new Map();
const addToRemoveQueue = (toastId)=>{
    if (toastTimeouts.has(toastId)) {
        return;
    }
    const timeout = setTimeout(()=>{
        toastTimeouts.delete(toastId);
        dispatch({
            type: "REMOVE_TOAST",
            toastId: toastId
        });
    }, TOAST_REMOVE_DELAY);
    toastTimeouts.set(toastId, timeout);
};
const reducer = (state, action)=>{
    switch(action.type){
        case "ADD_TOAST":
            return {
                ...state,
                toasts: [
                    action.toast,
                    ...state.toasts
                ].slice(0, TOAST_LIMIT)
            };
        case "UPDATE_TOAST":
            return {
                ...state,
                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {
                        ...t,
                        ...action.toast
                    } : t)
            };
        case "DISMISS_TOAST":
            {
                const { toastId } = action;
                // ! Side effects ! - This could be extracted into a dismissToast() action,
                // but I'll keep it here for simplicity
                if (toastId) {
                    addToRemoveQueue(toastId);
                } else {
                    state.toasts.forEach((toast)=>{
                        addToRemoveQueue(toast.id);
                    });
                }
                return {
                    ...state,
                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {
                            ...t,
                            open: false
                        } : t)
                };
            }
        case "REMOVE_TOAST":
            if (action.toastId === undefined) {
                return {
                    ...state,
                    toasts: []
                };
            }
            return {
                ...state,
                toasts: state.toasts.filter((t)=>t.id !== action.toastId)
            };
    }
};
const listeners = [];
let memoryState = {
    toasts: []
};
function dispatch(action) {
    memoryState = reducer(memoryState, action);
    listeners.forEach((listener)=>{
        listener(memoryState);
    });
}
function toast({ ...props }) {
    const id = genId();
    const update = (props)=>dispatch({
            type: "UPDATE_TOAST",
            toast: {
                ...props,
                id
            }
        });
    const dismiss = ()=>dispatch({
            type: "DISMISS_TOAST",
            toastId: id
        });
    dispatch({
        type: "ADD_TOAST",
        toast: {
            ...props,
            id,
            open: true,
            onOpenChange: (open)=>{
                if (!open) dismiss();
            }
        }
    });
    return {
        id: id,
        dismiss,
        update
    };
}
function useToast() {
    _s();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(memoryState);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useToast.useEffect": ()=>{
            listeners.push(setState);
            return ({
                "useToast.useEffect": ()=>{
                    const index = listeners.indexOf(setState);
                    if (index > -1) {
                        listeners.splice(index, 1);
                    }
                }
            })["useToast.useEffect"];
        }
    }["useToast.useEffect"], [
        state
    ]);
    return {
        ...state,
        toast,
        dismiss: (toastId)=>dispatch({
                type: "DISMISS_TOAST",
                toastId
            })
    };
}
_s(useToast, "SPWE98mLGnlsnNfIwu/IAKTSZtk=");
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/dictionary-context.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "DictionaryProvider": (()=>DictionaryProvider),
    "useDictionary": (()=>useDictionary)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-toast.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const DictionaryContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const DICTIONARY_STORAGE_KEY = 'lingua-flow-dictionary';
function DictionaryProvider({ children }) {
    _s();
    const [words, setWords] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DictionaryProvider.useEffect": ()=>{
            const storedWords = localStorage.getItem(DICTIONARY_STORAGE_KEY);
            if (storedWords) {
                try {
                    let parsedWords = JSON.parse(storedWords);
                    if (Array.isArray(parsedWords)) {
                        parsedWords = {
                            'en': parsedWords
                        };
                        localStorage.setItem(DICTIONARY_STORAGE_KEY, JSON.stringify(parsedWords));
                    }
                    if (typeof parsedWords === 'object' && !Array.isArray(parsedWords) && parsedWords !== null) {
                        Object.keys(parsedWords).forEach({
                            "DictionaryProvider.useEffect": (lang)=>{
                                if (Array.isArray(parsedWords[lang])) {
                                    parsedWords[lang].sort({
                                        "DictionaryProvider.useEffect": (a, b)=>a.localeCompare(b)
                                    }["DictionaryProvider.useEffect"]);
                                }
                            }
                        }["DictionaryProvider.useEffect"]);
                        setWords(parsedWords);
                    }
                } catch (e) {
                    console.error("Error parsing dictionary from localStorage", e);
                    localStorage.removeItem(DICTIONARY_STORAGE_KEY);
                }
            }
        }
    }["DictionaryProvider.useEffect"], []);
    const persistWords = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "DictionaryProvider.useCallback[persistWords]": (newWords)=>{
            localStorage.setItem(DICTIONARY_STORAGE_KEY, JSON.stringify(newWords));
            setWords(newWords);
        }
    }["DictionaryProvider.useCallback[persistWords]"], []);
    const addWord = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "DictionaryProvider.useCallback[addWord]": (word, lang)=>{
            const trimmedWord = word.trim();
            if (!trimmedWord) {
                toast({
                    titleKey: "toastDictionaryWordEmpty",
                    variant: "destructive"
                });
                return false;
            }
            const currentLangWords = words[lang] || [];
            if (currentLangWords.some({
                "DictionaryProvider.useCallback[addWord]": (w)=>w.toLowerCase() === trimmedWord.toLowerCase()
            }["DictionaryProvider.useCallback[addWord]"])) {
                toast({
                    titleKey: "toastInfoTitle",
                    descriptionKey: "toastDictionaryWordExists",
                    descriptionParams: {
                        word: trimmedWord
                    }
                });
                return false;
            }
            const newLangWords = [
                ...currentLangWords,
                trimmedWord
            ].sort({
                "DictionaryProvider.useCallback[addWord].newLangWords": (a, b)=>a.localeCompare(b)
            }["DictionaryProvider.useCallback[addWord].newLangWords"]);
            persistWords({
                ...words,
                [lang]: newLangWords
            });
            toast({
                titleKey: "toastSuccessTitle",
                descriptionKey: "toastDictionaryWordAdded",
                descriptionParams: {
                    word: trimmedWord
                }
            });
            return true;
        }
    }["DictionaryProvider.useCallback[addWord]"], [
        words,
        persistWords,
        toast
    ]);
    const deleteWord = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "DictionaryProvider.useCallback[deleteWord]": (wordToDelete, lang)=>{
            const currentLangWords = words[lang] || [];
            const newLangWords = currentLangWords.filter({
                "DictionaryProvider.useCallback[deleteWord].newLangWords": (w)=>w.toLowerCase() !== wordToDelete.toLowerCase()
            }["DictionaryProvider.useCallback[deleteWord].newLangWords"]);
            persistWords({
                ...words,
                [lang]: newLangWords
            });
            toast({
                titleKey: "toastSuccessTitle",
                descriptionKey: "toastDictionaryWordDeleted",
                descriptionParams: {
                    word: wordToDelete
                }
            });
        }
    }["DictionaryProvider.useCallback[deleteWord]"], [
        words,
        persistWords,
        toast
    ]);
    const importWords = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "DictionaryProvider.useCallback[importWords]": (newWordsToImport, lang, overwrite)=>{
            const uniqueNewWords = Array.from(new Set(newWordsToImport.map({
                "DictionaryProvider.useCallback[importWords].uniqueNewWords": (w)=>w.trim()
            }["DictionaryProvider.useCallback[importWords].uniqueNewWords"]).filter(Boolean)));
            const currentLangWords = words[lang] || [];
            if (overwrite) {
                const newLangWords = uniqueNewWords.sort({
                    "DictionaryProvider.useCallback[importWords].newLangWords": (a, b)=>a.localeCompare(b)
                }["DictionaryProvider.useCallback[importWords].newLangWords"]);
                persistWords({
                    ...words,
                    [lang]: newLangWords
                });
                toast({
                    titleKey: "toastSuccessTitle",
                    descriptionKey: "toastDictionaryImportOverwriteSuccess",
                    descriptionParams: {
                        count: newLangWords.length
                    }
                });
            } else {
                const combined = Array.from(new Set([
                    ...currentLangWords,
                    ...uniqueNewWords
                ])).sort({
                    "DictionaryProvider.useCallback[importWords].combined": (a, b)=>a.localeCompare(b)
                }["DictionaryProvider.useCallback[importWords].combined"]);
                persistWords({
                    ...words,
                    [lang]: combined
                });
                toast({
                    titleKey: "toastSuccessTitle",
                    descriptionKey: "toastDictionaryImportMergeSuccess",
                    descriptionParams: {
                        count: combined.length - currentLangWords.length
                    }
                });
            }
        }
    }["DictionaryProvider.useCallback[importWords]"], [
        words,
        persistWords,
        toast
    ]);
    const exportWordsString = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "DictionaryProvider.useCallback[exportWordsString]": (lang)=>{
            return JSON.stringify(words[lang] || [], null, 2);
        }
    }["DictionaryProvider.useCallback[exportWordsString]"], [
        words
    ]);
    const clearDictionary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "DictionaryProvider.useCallback[clearDictionary]": (lang)=>{
            const newWords = {
                ...words
            };
            delete newWords[lang];
            persistWords(newWords);
            toast({
                titleKey: "toastSuccessTitle",
                descriptionKey: "toastDictionaryCleared"
            });
        }
    }["DictionaryProvider.useCallback[clearDictionary]"], [
        words,
        persistWords,
        toast
    ]);
    const isWordInDictionary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "DictionaryProvider.useCallback[isWordInDictionary]": (word, lang)=>{
            const langWords = words[lang] || [];
            return langWords.some({
                "DictionaryProvider.useCallback[isWordInDictionary]": (w)=>w.toLowerCase() === word.trim().toLowerCase()
            }["DictionaryProvider.useCallback[isWordInDictionary]"]);
        }
    }["DictionaryProvider.useCallback[isWordInDictionary]"], [
        words
    ]);
    const getWordsForLanguage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "DictionaryProvider.useCallback[getWordsForLanguage]": (lang)=>{
            return words[lang] || [];
        }
    }["DictionaryProvider.useCallback[getWordsForLanguage]"], [
        words
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DictionaryContext.Provider, {
        value: {
            words,
            addWord,
            deleteWord,
            importWords,
            exportWordsString,
            clearDictionary,
            isWordInDictionary,
            getWordsForLanguage
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/dictionary-context.tsx",
        lineNumber: 121,
        columnNumber: 5
    }, this);
}
_s(DictionaryProvider, "A5dLtYLqOuSTPFS+01a1aJAP1ns=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"]
    ];
});
_c = DictionaryProvider;
function useDictionary() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(DictionaryContext);
    if (context === undefined) {
        throw new Error('useDictionary must be used within a DictionaryProvider');
    }
    return context;
}
_s1(useDictionary, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "DictionaryProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/feature-settings-context.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "FeatureSettingsProvider": (()=>FeatureSettingsProvider),
    "defaultFeatureSettings": (()=>defaultFeatureSettings),
    "useFeatureSettings": (()=>useFeatureSettings)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
const defaultFeatureSettings = {
    showAiSuggestionsOnSelection: true,
    quickAiActions: true,
    aiQuickReplies: true,
    promptHistory: true,
    autoCorrect: true,
    realTimeCorrection: true,
    sentenceEnhancement: true,
    showCorrectionFeedback: true,
    realTimeGrammarCheck: true,
    realTimeSpellCheck: true,
    styleSuggestions: true,
    // Writing Aid Features
    enableToneDetection: true,
    enablePlagiarismDetection: true,
    enableNonNativeSpeakerSupport: true,
    // Advanced Features
    enableOfflineFunctionality: true,
    enableAutomaticLanguageDetection: true
};
const FeatureSettingsContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const SETTINGS_STORAGE_KEY = 'lingua-flow-feature-settings';
function FeatureSettingsProvider({ children }) {
    _s();
    const [settings, setSettings] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultFeatureSettings);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FeatureSettingsProvider.useEffect": ()=>{
            const storedSettings = localStorage.getItem(SETTINGS_STORAGE_KEY);
            if (storedSettings) {
                try {
                    const parsedSettings = JSON.parse(storedSettings);
                    // Merge with defaults to ensure all keys are present, even if new ones are added later
                    setSettings({
                        ...defaultFeatureSettings,
                        ...parsedSettings
                    });
                } catch (e) {
                    console.error("Error parsing feature settings from localStorage", e);
                    // If parsing fails, stick with defaults and remove the broken item
                    localStorage.removeItem(SETTINGS_STORAGE_KEY);
                }
            }
        }
    }["FeatureSettingsProvider.useEffect"], []);
    const setFeatureSetting = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FeatureSettingsProvider.useCallback[setFeatureSetting]": (key, value)=>{
            setSettings({
                "FeatureSettingsProvider.useCallback[setFeatureSetting]": (prevSettings)=>{
                    const newSettings = {
                        ...prevSettings,
                        [key]: value
                    };
                    localStorage.setItem(SETTINGS_STORAGE_KEY, JSON.stringify(newSettings));
                    return newSettings;
                }
            }["FeatureSettingsProvider.useCallback[setFeatureSetting]"]);
        }
    }["FeatureSettingsProvider.useCallback[setFeatureSetting]"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(FeatureSettingsContext.Provider, {
        value: {
            settings,
            setFeatureSetting
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/feature-settings-context.tsx",
        lineNumber: 98,
        columnNumber: 5
    }, this);
}
_s(FeatureSettingsProvider, "KMqbDGQlPpjKyu9T30dTDJbt8Ng=");
_c = FeatureSettingsProvider;
function useFeatureSettings() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(FeatureSettingsContext);
    if (context === undefined) {
        throw new Error('useFeatureSettings must be used within a FeatureSettingsProvider');
    }
    return context;
}
_s1(useFeatureSettings, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "FeatureSettingsProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/config/languages.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "APP_SUPPORTED_UI_LANGUAGES": (()=>APP_SUPPORTED_UI_LANGUAGES),
    "APP_WRITING_LANGUAGES": (()=>APP_WRITING_LANGUAGES),
    "PROFICIENCY_LEVELS": (()=>PROFICIENCY_LEVELS)
});
const APP_SUPPORTED_UI_LANGUAGES = [
    {
        value: "en-US",
        labelKey: "englishUSLanguage",
        dir: "ltr"
    },
    {
        value: "en-GB",
        labelKey: "englishUKLanguage",
        dir: "ltr"
    },
    {
        value: "ar",
        labelKey: "arabicLanguage",
        dir: "rtl"
    },
    {
        value: "tr",
        labelKey: "turkishLanguage",
        dir: "ltr"
    },
    {
        value: "es",
        labelKey: "spanishLanguage",
        dir: "ltr"
    },
    {
        value: "de",
        labelKey: "germanLanguage",
        dir: "ltr"
    },
    {
        value: "fr",
        labelKey: "frenchLanguage",
        dir: "ltr"
    },
    {
        value: "nl",
        labelKey: "dutchLanguage",
        dir: "ltr"
    },
    {
        value: "it",
        labelKey: "italianLanguage",
        dir: "ltr"
    }
];
const APP_WRITING_LANGUAGES = [
    {
        value: "en",
        labelKey: "languageEnglishGeneral",
        dir: "ltr",
        dialects: [
            {
                value: "en-US",
                labelKey: "englishUSLanguage"
            },
            {
                value: "en-GB",
                labelKey: "englishUKLanguage"
            }
        ],
        supportsProficiency: true
    },
    {
        value: "es",
        labelKey: "languageSpanishGeneral",
        dir: "ltr",
        dialects: [
            {
                value: "es-ES",
                labelKey: "spanishSpainLanguage"
            },
            {
                value: "es-MX",
                labelKey: "spanishMexicoLanguage"
            }
        ],
        supportsProficiency: true
    },
    {
        value: "fr",
        labelKey: "languageFrenchGeneral",
        dir: "ltr",
        supportsProficiency: true
    },
    {
        value: "de",
        labelKey: "languageGermanGeneral",
        dir: "ltr",
        supportsProficiency: true
    },
    {
        value: "it",
        labelKey: "languageItalianGeneral",
        dir: "ltr",
        supportsProficiency: true
    },
    {
        value: "nl",
        labelKey: "languageDutchGeneral",
        dir: "ltr",
        supportsProficiency: true
    },
    {
        value: "ar",
        labelKey: "languageArabicGeneral",
        dir: "rtl",
        dialects: [
            {
                value: "ar-SY",
                labelKey: "arabicSyriaLanguage"
            },
            {
                value: "ar-SA",
                labelKey: "arabicSaudiArabiaLanguage"
            },
            {
                value: "ar-EG",
                labelKey: "arabicEgyptLanguage"
            }
        ],
        supportsProficiency: true
    },
    {
        value: "tr",
        labelKey: "languageTurkishGeneral",
        dir: "ltr",
        supportsProficiency: true
    }
];
const PROFICIENCY_LEVELS = [
    {
        value: 'native',
        labelKey: 'proficiencyNative'
    },
    {
        value: 'advanced',
        labelKey: 'proficiencyAdvanced'
    },
    {
        value: 'intermediate',
        labelKey: 'proficiencyIntermediate'
    },
    {
        value: 'beginner',
        labelKey: 'proficiencyBeginner'
    }
];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/locales/en-US.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"appName\":\"LinguaFlow\",\"appDescription\":\"Grammar Correction & Writing Assistant\",\"editorTitle\":\"Editor\",\"rephraseSelectionButton\":\"Rephrase Selection\",\"writingStatsTitle\":\"Writing Statistics\",\"wordCountLabel\":\"Word Count\",\"charCountLabel\":\"Character Count\",\"writingScoreLabel\":\"Writing Score\",\"writingScoreUnit\":\"/ 100\",\"writingModeLabel\":\"Writing Mode\",\"selectWritingModePlaceholder\":\"Select writing mode\",\"formalWritingMode\":\"Formal\",\"casualWritingMode\":\"Casual\",\"professionalWritingMode\":\"Professional\",\"creativeWritingMode\":\"Creative\",\"technicalWritingMode\":\"Technical\",\"academicWritingMode\":\"Academic\",\"businessWritingMode\":\"Business\",\"aiToneAnalysisAccordionTitle\":\"Tone Analysis\",\"aiToneAnalysisTitle\":\"Tone Analysis\",\"aiToneAnalysisDescription\":\"Get feedback on your writing's formality and confidence.\",\"analyzeToneButton\":\"Analyze Text Tone\",\"formalityLabel\":\"Formality\",\"confidenceLabel\":\"Confidence\",\"feedbackLabel\":\"Feedback\",\"writeSomeTextToAnalyzePlaceholder\":\"Write some text in the editor to analyze its tone.\",\"aiTextGenerationAccordionTitle\":\"AI Content Generation\",\"aiTextGenerationTitle\":\"AI Content Generator\",\"aiTextGenerationDescription\":\"Generate content based on your prompt.\",\"yourPromptLabel\":\"Your Prompt\",\"promptPlaceholder\":\"e.g., Write a short story about a robot who discovers music\",\"generatedTextLabel\":\"Generated Text\",\"generateTextButton\":\"Generate Text\",\"settingsAccordionTitle\":\"Settings\",\"settingsTitle\":\"Settings\",\"settingsDescription\":\"Customize your LinguaFlow experience.\",\"themeLabel\":\"Theme\",\"switchToLightMode\":\"Switch to Light Mode\",\"switchToDarkMode\":\"Switch to Dark Mode\",\"languageLabel\":\"Language\",\"selectLanguagePlaceholder\":\"Select language\",\"englishUSLanguage\":\"English (US)\",\"englishUKLanguage\":\"English (UK)\",\"arabicLanguage\":\"العربية (Arabic)\",\"turkishLanguage\":\"Türkçe (Turkish)\",\"spanishLanguage\":\"Español (Spanish)\",\"germanLanguage\":\"Deutsch (German)\",\"frenchLanguage\":\"Français (French)\",\"dutchLanguage\":\"Nederlands (Dutch)\",\"italianLanguage\":\"Italiano (Italian)\",\"startWritingPlaceholder\":\"Start writing here...\",\"rephrasePopoverTitle\":\"Rephrase Text\",\"rephrasePopoverDescription\":\"Review the suggestion for your selected text.\",\"originalTextLabel\":\"Original\",\"suggestionTextLabel\":\"Suggestion\",\"rephraseWaitMessage\":\"Click \\\"Rephrase\\\" or wait for suggestion.\",\"applyButton\":\"Apply\",\"cancelButton\":\"Cancel\",\"siteHeaderTitle\":\"LinguaFlow\",\"footerText\":\"Created by Eng: AZA7© 2025. All rights reserved®. Your support and recognition are greatly appreciated.\",\"toastInputRequiredTitle\":\"Input Required\",\"toastEditorEmptyError\":\"Editor is empty. Please write some text to analyze.\",\"toastPromptRequiredError\":\"Please enter a prompt to generate text.\",\"toastSuccessTitle\":\"Success\",\"toastErrorTitle\":\"Error\",\"toastInfoTitle\":\"Info\",\"toastTextGeneratedSuccess\":\"Text generated successfully.\",\"toastTextGenerationError\":\"Failed to generate text. Please try again.\",\"toastToneAnalysisSuccess\":\"Tone analysis complete.\",\"toastToneAnalysisError\":\"Failed to analyze tone. Please try again.\",\"toastNothingToRephraseError\":\"Nothing to rephrase\",\"toastSelectTextToRephraseError\":\"Please select some text in the editor.\",\"toastSuggestionReady\":\"Suggestion Ready\",\"toastRephraseError\":\"Failed to rephrase text. Please try again.\",\"toastFileUploadedSuccess\":\"File content loaded into editor.\",\"toastFileTypeNotSupportedError\":\"File type not supported. Please upload a {{fileType}} file.\",\"plagiarismDetectionAccordionTitle\":\"Plagiarism Detection\",\"plagiarismDetectionTitle\":\"Plagiarism Detection\",\"plagiarismDetectionSettingsTitle\":\"Plagiarism Detection\",\"plagiarismDetectionDescription\":\"Safeguard your integrity by utilizing our plagiarism detection tool, designed to meticulously scan your content for unintentional similarities with existing literature, helping you maintain originality in your writing.\",\"detectPlagiarismButton\":\"Detect Plagiarism Text\",\"originalityScoreLabel\":\"Originality Score\",\"plagiarismReportLabel\":\"Analysis Report\",\"potentialSourcesFoundLabel\":\"Potential Sources Found\",\"originalSourceLabel\":\"Original Source\",\"similarityScoreLabel\":\"Similarity Score\",\"toastPlagiarismDetectionSuccess\":\"Plagiarism check complete.\",\"toastPlagiarismDetectionError\":\"Failed to check for plagiarism. Please try again.\",\"writeSomeTextToDetectPlagiarismPlaceholder\":\"Write some text in the editor to check for plagiarism.\",\"aiWritingDetectionAccordionTitle\":\"AI Writing Detection\",\"aiWritingDetectionTitle\":\"AI Writing Detection\",\"aiWritingDetectionDescription\":\"Estimate the likelihood that your text was AI-generated.\",\"detectAiWritingButton\":\"Detect AI Writing Text\",\"probabilityAIWrittenLabel\":\"Probability AI Written\",\"aiWritingDetectionSummaryLabel\":\"Analysis Summary\",\"toastAiWritingDetectionSuccess\":\"AI writing detection complete.\",\"toastAiWritingDetectionError\":\"Failed to detect AI writing. Please try again.\",\"writeSomeTextToDetectAiWritingPlaceholder\":\"Write some text in the editor to check for AI authorship.\",\"writingSuggestionsTitle\":\"Writing Suggestions\",\"analyzingTextDescription\":\"AI is analyzing your text for suggestions...\",\"suggestionsFoundDescription\":\"{{count}} suggestions found. Review them below.\",\"noSuggestionsFoundDescription\":\"No immediate suggestions found. Keep writing or try rephrasing.\",\"startTypingForSuggestionsDescription\":\"Start typing for AI-powered writing suggestions.\",\"suggestionTypeSpelling\":\"Spelling\",\"suggestionTypeGrammar\":\"Grammar\",\"suggestionTypeRewrite\":\"Rewrite\",\"suggestionTypeStyle\":\"Style\",\"suggestionTypeUnknown\":\"Suggestion\",\"suggestionLabel\":\"Suggests\",\"applySuggestionButton\":\"Apply\",\"suggestionExplanationTooltip\":\"View explanation\",\"toastTextAnalysisError\":\"Failed to get writing suggestions. Please try again.\",\"toastSuggestionAppliedSuccess\":\"Suggestion applied.\",\"toastSuggestionApplyError\":\"Could not apply suggestion. The original text might have changed.\",\"humanizeAiTextAccordionTitle\":\"Humanize AI Text\",\"humanizeAiTextTitle\":\"Humanize AI Text\",\"humanizeAiTextDescription\":\"Rewrite AI-generated text to sound more human-like.\",\"humanizeTextButton\":\"Humanize Text\",\"humanizedTextLabel\":\"Humanized Text\",\"toastHumanizeTextSuccess\":\"Text humanized successfully.\",\"toastHumanizeTextError\":\"Failed to humanize text. Please try again.\",\"writeSomeTextToHumanizePlaceholder\":\"Write or paste AI-generated text in the editor to humanize it.\",\"clearEditorButton\":\"Clear\",\"clearEditorButtonAriaLabel\":\"Clear all text from the editor\",\"toastEditorClearedSuccess\":\"Editor content cleared.\",\"generationHistoryTitle\":\"Generation History\",\"noGenerationsYetPlaceholder\":\"No generations yet. Generate some text to see it here.\",\"promptLabel\":\"Prompt\",\"outputLabel\":\"Output\",\"useThisPromptButton\":\"Use this Prompt\",\"copyOutputButton\":\"Copy Output\",\"toastPromptRestoredSuccess\":\"Prompt restored to input field.\",\"toastTextCopiedSuccess\":\"Text copied to clipboard.\",\"toastTextCopyError\":\"Failed to copy text to clipboard.\",\"insertIntoEditorButton\":\"Insert into Editor\",\"insertIntoEditorButtonTooltip\":\"Append generated text to the editor\",\"toastTextInsertedSuccess\":\"Generated text inserted into editor.\",\"copyEditorButton\":\"Copy Text\",\"copyEditorButtonAriaLabel\":\"Copy all text from the editor\",\"toastEditorContentCopiedSuccess\":\"Editor content copied to clipboard.\",\"toastEditorContentCopyError\":\"Failed to copy editor content to clipboard.\",\"toastEditorEmptyForCopyError\":\"Editor is empty. Nothing to copy.\",\"recordVoiceButtonStart\":\"Record Voice\",\"recordVoiceButtonStop\":\"Stop Recording\",\"recordVoiceButtonAriaLabelStart\":\"Start voice recording to transcribe to text\",\"recordVoiceButtonAriaLabelStop\":\"Stop voice recording\",\"toastRecordingStarted\":\"Recording started. Speak into your microphone.\",\"toastRecordingStoppedNoTranscript\":\"Recording stopped. No speech was transcribed.\",\"toastSpeechTranscribedAndAppended\":\"Speech transcribed and appended to editor.\",\"toastSpeechRecognitionNotSupported\":\"Speech recognition is not supported by your browser.\",\"toastMicrophonePermissionDenied\":\"Microphone permission denied. Please enable it in your browser settings and refresh the page.\",\"toastSpeechNoSpeechDetected\":\"No speech was detected. Please try again.\",\"toastSpeechAudioCaptureError\":\"Audio capture error. Please check your microphone.\",\"toastSpeechNetworkError\":\"Network error during speech recognition. Please check your connection.\",\"toastSpeechRecognitionError\":\"Speech recognition error: {{error}}\",\"toastSpeechServiceNotAllowed\":\"Speech recognition service is not allowed or unavailable. Please try again later.\",\"toastSpeechLanguageNotSupportedError\":\"The selected language is not supported for speech recognition by your browser.\",\"helpTitle\":\"Help\",\"helpPanelTitle\":\"How to Use LinguaFlow\",\"helpPanelDescription\":\"Get started with LinguaFlow's features.\",\"helpPanelIntro\":\"Welcome to LinguaFlow! This guide will help you navigate and use the application's powerful writing assistance tools.\",\"helpEditorTitle\":\"The Editor\",\"helpEditorDescription\":\"The Editor is your main workspace. <br/><br/> <b>- Real-time Suggestions:</b> As you type, the app automatically checks your text and underlines potential issues. Click on a highlighted segment to see a correction popover. <br/> <b>- AI Tools on Selection:</b> Select any piece of text to bring up the <b>Smart Synonyms</b> button. If you select a single word, you'll get synonyms and a pronunciation guide. If you select a longer phrase, you'll get an AI-powered rephrasing suggestion. <br/> <b>- Formatting Toolbar:</b> At the top of the editor, you'll find tools to Undo/Redo, change fonts, and apply formatting like Bold, Italic, lists, and text alignment.\",\"helpAiToolsTitle\":\"AI Tools Panel\",\"helpAiToolsDescription\":\"The panels on the left and right provide powerful AI capabilities. <br/><br/> <b>- Write Tools (Left):</b> Here you can change the <b>Writing Mode</b> to influence the AI's style, <b>Import a Document</b>, or use the <b>AI Rewriter</b> to redraft your entire text. You can also use tools to <b>Humanize AI Text</b>, check for <b>AI Writing</b>, and detect <b>Plagiarism</b>. <br/> <b>- Analysis Tools (Right):</b> This column shows real-time <b>Writing Statistics</b>, provides a <b>Tone Analyzer</b> for your text, and includes the <b>AI Content Generator</b> to create new text from a prompt. Your generation history is saved here for easy reuse.\",\"helpLanguageSettingsTitle\":\"Language Settings\",\"helpLanguageSettingsDescription\":\"Configure the <b>UI Language</b> for the application's interface and the <b>Primary Writing Language</b> for analysis. For some languages, you can also select a <b>Regional Dialect</b>. Enable <b>Automatic Language Detection</b> to have the app switch writing languages as you type.\",\"helpAppearanceSettingsTitle\":\"Appearance Settings\",\"helpAppearanceSettingsDescription\":\"Customize the look and feel of the app. Choose a <b>Theme</b> (Light, Dark, or System), adjust the global <b>Font Size</b>, and toggle <b>High Contrast Mode</b> for better readability.\",\"helpDictionarySettingsTitle\":\"Dictionary Settings\",\"helpDictionarySettingsDescription\":\"Manage your personal dictionaries for different languages. <b>Add</b> words that you use often but might be flagged as misspellings (like names or technical jargon). You can also <b>Import</b> or <b>Export</b> your dictionary list as a JSON file, or <b>Clear</b> the dictionary for a specific language.\",\"helpFeatureSettingsTitle\":\"Feature Settings\",\"helpFeatureSettingsDescription\":\"Fine-tune the behavior of specific AI and auto-correction features. Here you can toggle on/off various generative AI tools, auto-correction behaviors, and the core real-time checking functionalities to match your workflow.\",\"helpWritingAidSettingsTitle\":\"Writing Aid Settings\",\"helpWritingAidSettingsDescription\":\"Customize how the AI assists you. Set your <b>Language Proficiency</b> to get suggestions tailored to your skill level. You can also enable or disable features like <b>Tone Detection</b>, <b>Plagiarism Detection</b>, and specialized support for <b>Non-native Speakers</b>.\",\"helpAdvancedSettingsTitle\":\"Advanced Settings\",\"helpAdvancedSettingsDescription\":\"Control core operational behaviors. Enable <b>Offline Functionality</b> to use basic features without an internet connection. If you ever want to start fresh, you can <b>Reset All Settings</b> to restore the application to its original defaults (this cannot be undone).\",\"helpPanelTip\":\"Experiment with different tools and settings to find what works best for your writing style and needs!\",\"Write Tools\":\"Write Tools\",\"Import Document\":\"Import Document\",\"Quick Action\":\"Quick Action\",\"Tone Analyzer\":\"Tone Analyzer\",\"aiRewriteAccordionTitle\":\"AI Rewriter\",\"aiRewriteTitle\":\"AI Rewriter\",\"aiRewriteDescription\":\"Rewrite the entire editor content to enhance clarity and style.\",\"rewriteEditorContentButton\":\"Rewrite Editor Content\",\"rewrittenTextLabel\":\"Rewritten Text\",\"applyToEditorButton\":\"Apply to Editor\",\"toastRewriteSuccess\":\"Editor content rewritten successfully.\",\"toastRewriteError\":\"Failed to rewrite editor content. Please try again.\",\"writeSomeTextToRewritePlaceholder\":\"Write some text in the editor to rewrite it.\",\"Click the button to rewrite the editor content.\":\"Click the button to rewrite the editor content.\",\"dropzoneInstruction\":\"Drop files here or browse\",\"toastFileImportSuccessTitle\":\"File Imported\",\"toastFileImportSuccessMessage\":\"Document content has been loaded.\",\"toastFileImportErrorTitle\":\"Import Error\",\"toastFileImportErrorMessage\":\"Could not read the file content. Please ensure it's a valid .txt file.\",\"toastInvalidFileTypeMessage\":\"Invalid file type. Only .txt files are accepted.\",\"dropzoneAriaLabel\":\"Document import dropzone: Click or drag and drop a .txt file to upload.\",\"featuresLabel\":\"Features\",\"featureSettingsDescription\":\"Customize the functionality of specific writing assistance features.\",\"appearanceLabel\":\"Appearance\",\"writingAidLabel\":\"Writing Aid\",\"dictionaryLabel\":\"Dictionary\",\"dictionarySettingsDescription\":\"Add custom words or manage personal dictionaries. (Placeholder)\",\"advancedSettingsLabel\":\"Advanced\",\"advancedSettingsDescription\":\"Access advanced configuration options. Use with caution. (Placeholder)\",\"uiLanguageLabel\":\"UI Language\",\"selectUiLanguagePlaceholder\":\"Select UI language...\",\"uiLanguageDescription\":\"Changes the language of the application interface.\",\"writingLanguageLabel\":\"Primary Writing Language\",\"selectWritingLanguagePlaceholder\":\"Select writing language...\",\"writingLanguageDescription\":\"Sets the primary language for AI analysis and generation.\",\"regionalDialectLabel\":\"Regional Dialect\",\"selectRegionalDialectPlaceholder\":\"Select dialect...\",\"regionalDialectDescription\":\"Specifies the regional variation for the selected writing language.\",\"languageProficiencyLabel\":\"Language Proficiency\",\"selectProficiencyPlaceholder\":\"Select proficiency...\",\"languageProficiencyDescription\":\"Helps AI tailor suggestions to your language skill level.\",\"proficiencyNative\":\"Native\",\"proficiencyAdvanced\":\"Advanced (C1/C2)\",\"proficiencyIntermediate\":\"Intermediate (B1/B2)\",\"proficiencyBeginner\":\"Beginner (A1/A2)\",\"languageEnglishGeneral\":\"English\",\"languageSpanishGeneral\":\"Spanish\",\"languageFrenchGeneral\":\"French\",\"languageGermanGeneral\":\"German\",\"languageItalianGeneral\":\"Italian\",\"languageDutchGeneral\":\"Dutch\",\"languageArabicGeneral\":\"Arabic\",\"arabicSyriaLanguage\":\"Arabic (Syria)\",\"arabicSaudiArabiaLanguage\":\"Arabic (Saudi Arabia)\",\"arabicEgyptLanguage\":\"Arabic (Egypt)\",\"languageTurkishGeneral\":\"Turkish\",\"spanishSpainLanguage\":\"Spanish (Spain)\",\"spanishMexicoLanguage\":\"Spanish (Mexico)\",\"themeLight\":\"Light\",\"themeDark\":\"Dark\",\"themeSystem\":\"System Default\",\"selectThemePlaceholder\":\"Select theme...\",\"themeDescription\":\"Choose the application's visual theme.\",\"fontSizeLabel\":\"Font Size\",\"selectFontSizePlaceholder\":\"Select font size...\",\"fontSizeSmall\":\"Small\",\"fontSizeMedium\":\"Medium\",\"fontSizeLarge\":\"Large\",\"fontSizeDescription\":\"Adjust the text size throughout the application.\",\"highContrastModeLabel\":\"High Contrast Mode\",\"highContrastModeDescription\":\"Increases text/background contrast for better readability.\",\"enabledLabel\":\"Enabled\",\"disabledLabel\":\"Disabled\",\"personalDictionaryLabel\":\"Personal Dictionary\",\"personalDictionaryDescription\":\"Add words you use frequently that might be flagged as errors.\",\"addWordPlaceholder\":\"Enter a word...\",\"addWordButton\":\"Add Word\",\"deleteWordButtonAria\":\"Delete word {{word}}\",\"dictionaryEmptyPlaceholder\":\"Your dictionary is empty. Add some words!\",\"dictionaryImportExportLabel\":\"Import / Export Dictionary\",\"importDictionaryButton\":\"Import\",\"exportDictionaryButton\":\"Export\",\"dictionaryImportExportDescription\":\"Backup or share your personal dictionary as a JSON file.\",\"clearDictionaryForLanguageButton\":\"Clear {{language}} Dictionary\",\"clearDictionaryConfirmTitle\":\"Are you sure?\",\"clearDictionaryForLanguageConfirmDescription\":\"This will permanently delete all words from your personal dictionary for {{language}}. This action cannot be undone.\",\"confirmResetButton\":\"Yes, Reset Everything\",\"clearDictionaryWarning\":\"This action is irreversible.\",\"toastDictionaryWordAdded\":\"Word '{{word}}' added to dictionary.\",\"toastDictionaryWordExists\":\"Word '{{word}}' already exists in dictionary.\",\"toastDictionaryWordEmpty\":\"Cannot add an empty word.\",\"toastDictionaryWordDeleted\":\"Word '{{word}}' deleted from dictionary.\",\"toastDictionaryImportOverwriteSuccess\":\"{{count}} words imported, dictionary overwritten.\",\"toastDictionaryImportMergeSuccess\":\"{{count}} new words imported and merged.\",\"toastDictionaryImportInvalidFormat\":\"Invalid dictionary file format. Must be a JSON array of strings.\",\"toastDictionaryImportError\":\"Error importing dictionary file.\",\"toastDictionaryExportSuccess\":\"Dictionary exported successfully.\",\"toastDictionaryCleared\":\"Personal dictionary cleared.\",\"generativeAiFeaturesLabel\":\"Generative AI Features\",\"showAiSuggestionsOnTextSelectionLabel\":\"Show AI Suggestions on Text Selection\",\"showAiSuggestionsOnTextSelectionDescription\":\"Enable AI suggestions when you select text.\",\"quickAiActionsForSelectedTextLabel\":\"Quick AI Actions for Selected Text\",\"quickAiActionsForSelectedTextDescription\":\"Access quick AI actions for highlighted text.\",\"aiQuickReplySuggestionsLabel\":\"AI-Based Quick Reply Suggestions\",\"aiQuickReplySuggestionsDescription\":\"Receive ideas for replies. (Mocked feature)\",\"viewAndReuseRecentPromptHistoryLabel\":\"View and Reuse Recent Prompt History\",\"viewAndReuseRecentPromptHistoryDescription\":\"Easily access your recent AI prompts.\",\"autoCorrectionFeaturesLabel\":\"Auto-Correction Features\",\"automaticTextCorrectionLabel\":\"Automatic Text Correction\",\"automaticTextCorrectionDescription\":\"Automatically fix common typos and grammatical errors.\",\"realTimeCorrectionLabel\":\"Real-Time Correction\",\"realTimeCorrectionDescription\":\"Apply corrections as you type.\",\"sentenceEnhancementLabel\":\"Sentence Enhancement\",\"sentenceEnhancementDescription\":\"Get suggestions for improving clarity and style.\",\"showCorrectionFeedbackLabel\":\"Show Correction Feedback\",\"showCorrectionFeedbackDescription\":\"Receive notifications when corrections are applied.\",\"coreCheckingFeaturesLabel\":\"Core Checking Features\",\"realTimeGrammarCheckingLabel\":\"Real-Time Grammar Checking\",\"realTimeGrammarCheckingDescription\":\"Get instant feedback on grammar as you write.\",\"realTimeSpellCheckingLabel\":\"Real-Time Spell Checking\",\"realTimeSpellCheckingDescription\":\"Check for spelling mistakes in real time.\",\"styleSuggestionsLabel\":\"Style Suggestions\",\"styleSuggestionsDescription\":\"Get suggestions to improve your writing style.\",\"toastLanguageSwitched\":\"Writing language automatically switched to {{language}}.\",\"writingAssistanceTitle\":\"Writing Assistance\",\"writingAssistanceDescription\":\"Enhance your writing experience with LinguaFlow by customizing how it assists you.\",\"yourLanguageProficiencyTitle\":\"Your Language Proficiency (for Primary Language)\",\"yourLanguageProficiencyDescription\":\"Receive meticulously tailored suggestions that align with your sophisticated understanding of language, ensuring a more nuanced approach to your writing.\",\"toneDetectionTitle\":\"Tone Detection\",\"toneDetectionDescription\":\"Delve into the emotional undertones of your text with our tone detection feature, which analyzes your writing and offers insightful suggestions to refine and elevate your tone, making it resonate more effectively with your audience.\",\"nonNativeSupportTitle\":\"Support for Non-native Speakers\",\"nonNativeSupportDescription\":\"Benefit from specialized assistance aimed at non-native speakers, providing thoughtful guidance and practical tips to enhance your fluency and confidence in writing.\",\"advancedSettingsTitle\":\"Advanced Settings\",\"enableOfflineFunctionalityLabel\":\"Enable Offline Functionality\",\"enableOfflineFunctionalityDescription\":\"Basic features, including settings and the dictionary, are available offline. Key components will be included to ensure seamless program function.\",\"enableAutomaticLanguageDetectionLabel\":\"Enable Automatic Language Detection\",\"enableAutomaticLanguageDetectionDescription\":\"Automatically detect and switch the writing language as you type.\",\"dataManagementLabel\":\"Data Management\",\"resetAllSettingsLabel\":\"Reset All Settings\",\"resetAllSettingsDescription\":\"This will reset all customizations including theme, language, and feature settings to their defaults. This action cannot be undone.\",\"resetButtonLabel\":\"Reset\",\"resetAllSettingsConfirmTitle\":\"Are you sure you want to reset all settings?\",\"resetAllSettingsConfirmDescription\":\"All your personal settings, dictionary words, and preferences will be permanently deleted and reset to the application defaults. This action cannot be undone.\",\"toastResetSuccess\":\"All settings have been reset to default. The application will now reload.\",\"dictionaryLanguageLabel\":\"Dictionary Language\",\"selectDictionaryLanguagePlaceholder\":\"Select language to view...\",\"dictionaryLanguageDescription\":\"View and manage the dictionary for a specific language.\",\"toastSuggestionDismissed\":\"Suggestion dismissed.\",\"dismissButton\":\"Dismiss\",\"correctButton\":\"Correct\",\"undoButton\":\"Undo\",\"redoButton\":\"Redo\",\"aiToolsButton\":\"Smart Synonyms\",\"wordToolkitTitle\":\"Synonyms Suggestion\",\"wordToolkitDescription\":\"Select a single word in the editor to get synonyms and hear its pronunciation.\",\"wordToolkitPlaceholder\":\"Select a single word in the main editor to get smart synonyms.\",\"selectedWordLabel\":\"Selected Word\",\"synonymsLabel\":\"Synonyms\",\"noSynonymsFound\":\"No synonyms found.\",\"applySynonymTooltip\":\"Replace word with '{{synonym}}'\",\"toastWordToolkitError\":\"Failed to get suggestions for the selected word.\",\"toastWordReplacedSuccess\":\"Word replaced with '{{word}}'.\",\"wordToolkitPopoverDescription\":\"Get synonyms or hear the word's pronunciation.\",\"pronunciationLabel\":\"Pronunciation\",\"pronounceButton\":\"Pronounce Word\",\"toastPronunciationError\":\"Failed to generate audio pronunciation.\",\"toastEmptyText\":\"Please enter some text first.\"}"));}}),
"[project]/src/locales/en-GB.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"appName\":\"LinguaFlow\",\"appDescription\":\"Grammar Correction & Writing Assistant\",\"editorTitle\":\"Editor\",\"rephraseSelectionButton\":\"Rephrase Selection\",\"writingStatsTitle\":\"Writing Statistics\",\"wordCountLabel\":\"Word Count\",\"charCountLabel\":\"Character Count\",\"writingScoreLabel\":\"Writing Score\",\"writingScoreUnit\":\"/ 100\",\"writingModeLabel\":\"Writing Mode\",\"selectWritingModePlaceholder\":\"Select writing mode\",\"formalWritingMode\":\"Formal\",\"casualWritingMode\":\"Casual\",\"professionalWritingMode\":\"Professional\",\"creativeWritingMode\":\"Creative\",\"technicalWritingMode\":\"Technical\",\"academicWritingMode\":\"Academic\",\"businessWritingMode\":\"Business\",\"aiToneAnalysisAccordionTitle\":\"Tone Analysis\",\"aiToneAnalysisTitle\":\"Tone Analysis\",\"aiToneAnalysisDescription\":\"Get feedback on your writing's formality and confidence.\",\"analyzeToneButton\":\"Analyse Text Tone\",\"formalityLabel\":\"Formality\",\"confidenceLabel\":\"Confidence\",\"feedbackLabel\":\"Feedback\",\"writeSomeTextToAnalyzePlaceholder\":\"Write some text in the editor to analyse its tone.\",\"aiTextGenerationAccordionTitle\":\"AI Content Generation\",\"aiTextGenerationTitle\":\"AI Content Generator\",\"aiTextGenerationDescription\":\"Generate content based on your prompt.\",\"yourPromptLabel\":\"Your Prompt\",\"promptPlaceholder\":\"e.g., Write a short story about a robot who discovers music\",\"generatedTextLabel\":\"Generated Text\",\"generateTextButton\":\"Generate Text\",\"settingsAccordionTitle\":\"Settings\",\"settingsTitle\":\"Settings\",\"settingsDescription\":\"Customise your LinguaFlow experience.\",\"themeLabel\":\"Theme\",\"switchToLightMode\":\"Switch to Light Mode\",\"switchToDarkMode\":\"Switch to Dark Mode\",\"languageLabel\":\"Language\",\"selectLanguagePlaceholder\":\"Select language\",\"englishUSLanguage\":\"English (US)\",\"englishUKLanguage\":\"English (UK)\",\"arabicLanguage\":\"العربية (Arabic)\",\"turkishLanguage\":\"Türkçe (Turkish)\",\"spanishLanguage\":\"Español (Spanish)\",\"germanLanguage\":\"Deutsch (German)\",\"frenchLanguage\":\"Français (French)\",\"dutchLanguage\":\"Nederlands (Dutch)\",\"italianLanguage\":\"Italiano (Italian)\",\"startWritingPlaceholder\":\"Start writing here...\",\"rephrasePopoverTitle\":\"Rephrase Text\",\"rephrasePopoverDescription\":\"Review the suggestion for your selected text.\",\"originalTextLabel\":\"Original\",\"suggestionTextLabel\":\"Suggestion\",\"rephraseWaitMessage\":\"Click \\\"Rephrase\\\" or wait for suggestion.\",\"applyButton\":\"Apply\",\"cancelButton\":\"Cancel\",\"siteHeaderTitle\":\"LinguaFlow\",\"footerText\":\"Created by Eng: AZA7© 2025. All rights reserved®. Your support and recognition are greatly appreciated.\",\"toastInputRequiredTitle\":\"Input Required\",\"toastEditorEmptyError\":\"Editor is empty. Please write some text to analyse.\",\"toastPromptRequiredError\":\"Please enter a prompt to generate text.\",\"toastSuccessTitle\":\"Success\",\"toastErrorTitle\":\"Error\",\"toastInfoTitle\":\"Info\",\"toastTextGeneratedSuccess\":\"Text generated successfully.\",\"toastTextGenerationError\":\"Failed to generate text. Please try again.\",\"toastToneAnalysisSuccess\":\"Tone analysis complete.\",\"toastToneAnalysisError\":\"Failed to analyse tone. Please try again.\",\"toastNothingToRephraseError\":\"Nothing to rephrase\",\"toastSelectTextToRephraseError\":\"Please select some text in the editor.\",\"toastSuggestionReady\":\"Suggestion Ready\",\"toastRephraseError\":\"Failed to rephrase text. Please try again.\",\"toastFileUploadedSuccess\":\"File content loaded into editor.\",\"toastFileTypeNotSupportedError\":\"File type not supported. Please upload a {{fileType}} file.\",\"plagiarismDetectionAccordionTitle\":\"Plagiarism Detection\",\"plagiarismDetectionTitle\":\"Plagiarism Detection\",\"plagiarismDetectionSettingsTitle\":\"Plagiarism Detection\",\"plagiarismDetectionDescription\":\"Safeguard your integrity by utilising our plagiarism detection tool, designed to meticulously scan your content for unintentional similarities with existing literature, helping you maintain originality in your writing.\",\"detectPlagiarismButton\":\"Detect Plagiarism Text\",\"originalityScoreLabel\":\"Originality Score\",\"plagiarismReportLabel\":\"Analysis Report\",\"potentialSourcesFoundLabel\":\"Potential Sources Found\",\"originalSourceLabel\":\"Original Source\",\"similarityScoreLabel\":\"Similarity Score\",\"toastPlagiarismDetectionSuccess\":\"Plagiarism check complete.\",\"toastPlagiarismDetectionError\":\"Failed to check for plagiarism. Please try again.\",\"writeSomeTextToDetectPlagiarismPlaceholder\":\"Write some text in the editor to check for plagiarism.\",\"aiWritingDetectionAccordionTitle\":\"AI Writing Detection\",\"aiWritingDetectionTitle\":\"AI Writing Detection\",\"aiWritingDetectionDescription\":\"Estimate the likelihood that your text was AI-generated.\",\"detectAiWritingButton\":\"Detect AI Writing Text\",\"probabilityAIWrittenLabel\":\"Probability AI Written\",\"aiWritingDetectionSummaryLabel\":\"Analysis Summary\",\"toastAiWritingDetectionSuccess\":\"AI writing detection complete.\",\"toastAiWritingDetectionError\":\"Failed to detect AI writing. Please try again.\",\"writeSomeTextToDetectAiWritingPlaceholder\":\"Write some text in the editor to check for AI authorship.\",\"writingSuggestionsTitle\":\"Writing Suggestions\",\"analyzingTextDescription\":\"AI is analysing your text for suggestions...\",\"suggestionsFoundDescription\":\"{{count}} suggestions found. Review them below.\",\"noSuggestionsFoundDescription\":\"No immediate suggestions found. Keep writing or try rephrasing.\",\"startTypingForSuggestionsDescription\":\"Start typing for AI-powered writing suggestions.\",\"suggestionTypeSpelling\":\"Spelling\",\"suggestionTypeGrammar\":\"Grammar\",\"suggestionTypeRewrite\":\"Rewrite\",\"suggestionTypeStyle\":\"Style\",\"suggestionTypeUnknown\":\"Suggestion\",\"suggestionLabel\":\"Suggests\",\"applySuggestionButton\":\"Apply\",\"suggestionExplanationTooltip\":\"View explanation\",\"toastTextAnalysisError\":\"Failed to get writing suggestions. Please try again.\",\"toastSuggestionAppliedSuccess\":\"Suggestion applied.\",\"toastSuggestionApplyError\":\"Could not apply suggestion. The original text might have changed.\",\"humanizeAiTextAccordionTitle\":\"Humanise AI Text\",\"humanizeAiTextTitle\":\"Humanise AI Text\",\"humanizeAiTextDescription\":\"Rewrite AI-generated text to sound more human-like.\",\"humanizeTextButton\":\"Humanise Text\",\"humanizedTextLabel\":\"Humanised Text\",\"toastHumanizeTextSuccess\":\"Text humanised successfully.\",\"toastHumanizeTextError\":\"Failed to humanise text. Please try again.\",\"writeSomeTextToHumanizePlaceholder\":\"Write or paste AI-generated text in the editor to humanise it.\",\"clearEditorButton\":\"Clear\",\"clearEditorButtonAriaLabel\":\"Clear all text from the editor\",\"toastEditorClearedSuccess\":\"Editor content cleared.\",\"generationHistoryTitle\":\"Generation History\",\"noGenerationsYetPlaceholder\":\"No generations yet. Generate some text to see it here.\",\"promptLabel\":\"Prompt\",\"outputLabel\":\"Output\",\"useThisPromptButton\":\"Use this Prompt\",\"copyOutputButton\":\"Copy Output\",\"toastPromptRestoredSuccess\":\"Prompt restored to input field.\",\"toastTextCopiedSuccess\":\"Text copied to clipboard.\",\"toastTextCopyError\":\"Failed to copy text to clipboard.\",\"insertIntoEditorButton\":\"Insert into Editor\",\"insertIntoEditorButtonTooltip\":\"Append generated text to the editor\",\"toastTextInsertedSuccess\":\"Generated text inserted into editor.\",\"copyEditorButton\":\"Copy Text\",\"copyEditorButtonAriaLabel\":\"Copy all text from the editor\",\"toastEditorContentCopiedSuccess\":\"Editor content copied to clipboard.\",\"toastEditorContentCopyError\":\"Failed to copy editor content to clipboard.\",\"toastEditorEmptyForCopyError\":\"Editor is empty. Nothing to copy.\",\"recordVoiceButtonStart\":\"Record Voice\",\"recordVoiceButtonStop\":\"Stop Recording\",\"recordVoiceButtonAriaLabelStart\":\"Start voice recording to transcribe to text\",\"recordVoiceButtonAriaLabelStop\":\"Stop voice recording\",\"toastRecordingStarted\":\"Recording started. Speak into your microphone.\",\"toastRecordingStoppedNoTranscript\":\"Recording stopped. No speech was transcribed.\",\"toastSpeechTranscribedAndAppended\":\"Speech transcribed and appended to editor.\",\"toastSpeechRecognitionNotSupported\":\"Speech recognition is not supported by your browser.\",\"toastMicrophonePermissionDenied\":\"Microphone permission denied. Please enable it in your browser settings and refresh the page.\",\"toastSpeechNoSpeechDetected\":\"No speech was detected. Please try again.\",\"toastSpeechAudioCaptureError\":\"Audio capture error. Please check your microphone.\",\"toastSpeechNetworkError\":\"Network error during speech recognition. Please check your connection.\",\"toastSpeechRecognitionError\":\"Speech recognition error: {{error}}\",\"toastSpeechServiceNotAllowed\":\"Speech recognition service is not allowed or unavailable. Please try again later.\",\"toastSpeechLanguageNotSupportedError\":\"The selected language is not supported for speech recognition by your browser.\",\"helpTitle\":\"Help\",\"helpPanelTitle\":\"How to Use LinguaFlow\",\"helpPanelDescription\":\"Get started with LinguaFlow's features.\",\"helpPanelIntro\":\"Welcome to LinguaFlow! This guide will help you navigate and use the application's powerful writing assistance tools.\",\"helpEditorTitle\":\"The Editor\",\"helpEditorDescription\":\"The Editor is your main workspace. <br/><br/> <b>- Real-time Suggestions:</b> As you type, the app automatically checks your text and underlines potential issues. Click on a highlighted segment to see a correction popover. <br/> <b>- AI Tools on Selection:</b> Select any piece of text to bring up the <b>Smart Synonyms</b> button. If you select a single word, you'll get synonyms and a pronunciation guide. If you select a longer phrase, you'll get an AI-powered rephrasing suggestion. <br/> <b>- Formatting Toolbar:</b> At the top of the editor, you'll find tools to Undo/Redo, change fonts, and apply formatting like Bold, Italic, lists, and text alignment.\",\"helpAiToolsTitle\":\"AI Tools Panel\",\"helpAiToolsDescription\":\"The panels on the left and right provide powerful AI capabilities. <br/><br/> <b>- Write Tools (Left):</b> Here you can change the <b>Writing Mode</b> to influence the AI's style, <b>Import a Document</b>, or use the <b>AI Rewriter</b> to redraft your entire text. You can also use tools to <b>Humanise AI Text</b>, check for <b>AI Writing</b>, and detect <b>Plagiarism</b>. <br/> <b>- Analysis Tools (Right):</b> This column shows real-time <b>Writing Statistics</b>, provides a <b>Tone Analyser</b> for your text, and includes the <b>AI Content Generator</b> to create new text from a prompt. Your generation history is saved here for easy reuse.\",\"helpLanguageSettingsTitle\":\"Language Settings\",\"helpLanguageSettingsDescription\":\"Configure the <b>UI Language</b> for the application's interface and the <b>Primary Writing Language</b> for analysis. For some languages, you can also select a <b>Regional Dialect</b>. Enable <b>Automatic Language Detection</b> to have the app switch writing languages as you type.\",\"helpAppearanceSettingsTitle\":\"Appearance Settings\",\"helpAppearanceSettingsDescription\":\"Customise the look and feel of the app. Choose a <b>Theme</b> (Light, Dark, or System), adjust the global <b>Font Size</b>, and toggle <b>High Contrast Mode</b> for better readability.\",\"helpDictionarySettingsTitle\":\"Dictionary Settings\",\"helpDictionarySettingsDescription\":\"Manage your personal dictionaries for different languages. <b>Add</b> words that you use often but might be flagged as misspellings (like names or technical jargon). You can also <b>Import</b> or <b>Export</b> your dictionary list as a JSON file, or <b>Clear</b> the dictionary for a specific language.\",\"helpFeatureSettingsTitle\":\"Feature Settings\",\"helpFeatureSettingsDescription\":\"Fine-tune the behaviour of specific AI and auto-correction features. Here you can toggle on/off various generative AI tools, auto-correction behaviours, and the core real-time checking functionalities to match your workflow.\",\"helpWritingAidSettingsTitle\":\"Writing Aid Settings\",\"helpWritingAidSettingsDescription\":\"Customise how the AI assists you. Set your <b>Language Proficiency</b> to get suggestions tailored to your skill level. You can also enable or disable features like <b>Tone Detection</b>, <b>Plagiarism Detection</b>, and specialised support for <b>Non-native Speakers</b>.\",\"helpAdvancedSettingsTitle\":\"Advanced Settings\",\"helpAdvancedSettingsDescription\":\"Control core operational behaviours. Enable <b>Offline Functionality</b> to use basic features without an internet connection. If you ever want to start fresh, you can <b>Reset All Settings</b> to restore the application to its original defaults (this cannot be undone).\",\"helpPanelTip\":\"Experiment with different tools and settings to find what works best for your writing style and needs!\",\"Write Tools\":\"Write Tools\",\"Import Document\":\"Import Document\",\"Quick Action\":\"Quick Action\",\"Tone Analyzer\":\"Tone Analyser\",\"aiRewriteAccordionTitle\":\"AI Rewriter\",\"aiRewriteTitle\":\"AI Rewriter\",\"aiRewriteDescription\":\"Rewrite the entire editor content to enhance clarity and style.\",\"rewriteEditorContentButton\":\"Rewrite Editor Content\",\"rewrittenTextLabel\":\"Rewritten Text\",\"applyToEditorButton\":\"Apply to Editor\",\"toastRewriteSuccess\":\"Editor content rewritten successfully.\",\"toastRewriteError\":\"Failed to rewrite editor content. Please try again.\",\"writeSomeTextToRewritePlaceholder\":\"Write some text in the editor to rewrite it.\",\"Click the button to rewrite the editor content.\":\"Click the button to rewrite the editor content.\",\"dropzoneInstruction\":\"Drop files here or browse\",\"toastFileImportSuccessTitle\":\"File Imported\",\"toastFileImportSuccessMessage\":\"Document content has been loaded.\",\"toastFileImportErrorTitle\":\"Import Error\",\"toastFileImportErrorMessage\":\"Could not read the file content. Please ensure it's a valid .txt file.\",\"toastInvalidFileTypeMessage\":\"Invalid file type. Only .txt files are accepted.\",\"dropzoneAriaLabel\":\"Document import dropzone: Click or drag and drop a .txt file to upload.\",\"featuresLabel\":\"Features\",\"featureSettingsDescription\":\"Customise the functionality of specific writing assistance features.\",\"appearanceLabel\":\"Appearance\",\"writingAidLabel\":\"Writing Aid\",\"dictionaryLabel\":\"Dictionary\",\"dictionarySettingsDescription\":\"Add custom words or manage personal dictionaries. (Placeholder)\",\"advancedSettingsLabel\":\"Advanced\",\"advancedSettingsDescription\":\"Access advanced configuration options. Use with caution. (Placeholder)\",\"uiLanguageLabel\":\"UI Language\",\"selectUiLanguagePlaceholder\":\"Select UI language...\",\"uiLanguageDescription\":\"Changes the language of the application interface.\",\"writingLanguageLabel\":\"Primary Writing Language\",\"selectWritingLanguagePlaceholder\":\"Select writing language...\",\"writingLanguageDescription\":\"Sets the primary language for AI analysis and generation.\",\"regionalDialectLabel\":\"Regional Dialect\",\"selectRegionalDialectPlaceholder\":\"Select dialect...\",\"regionalDialectDescription\":\"Specifies the regional variation for the selected writing language.\",\"languageProficiencyLabel\":\"Language Proficiency\",\"selectProficiencyPlaceholder\":\"Select proficiency...\",\"languageProficiencyDescription\":\"Helps AI tailor suggestions to your language skill level.\",\"proficiencyNative\":\"Native\",\"proficiencyAdvanced\":\"Advanced (C1/C2)\",\"proficiencyIntermediate\":\"Intermediate (B1/B2)\",\"proficiencyBeginner\":\"Beginner (A1/A2)\",\"languageEnglishGeneral\":\"English\",\"languageSpanishGeneral\":\"Spanish\",\"languageFrenchGeneral\":\"French\",\"languageGermanGeneral\":\"German\",\"languageItalianGeneral\":\"Italian\",\"languageDutchGeneral\":\"Dutch (Netherlands)\",\"languageArabicGeneral\":\"Arabic\",\"arabicSyriaLanguage\":\"Arabic (Syria)\",\"arabicSaudiArabiaLanguage\":\"Arabic (Saudi Arabia)\",\"arabicEgyptLanguage\":\"Arabic (Egypt)\",\"languageTurkishGeneral\":\"Turkish\",\"spanishSpainLanguage\":\"Spanish (Spain)\",\"spanishMexicoLanguage\":\"Spanish (Mexico)\",\"themeLight\":\"Light\",\"themeDark\":\"Dark\",\"themeSystem\":\"System Default\",\"selectThemePlaceholder\":\"Select theme...\",\"themeDescription\":\"Choose the application's visual theme.\",\"fontSizeLabel\":\"Font Size\",\"selectFontSizePlaceholder\":\"Select font size...\",\"fontSizeSmall\":\"Small\",\"fontSizeMedium\":\"Medium\",\"fontSizeLarge\":\"Large\",\"fontSizeDescription\":\"Adjust the text size throughout the application.\",\"highContrastModeLabel\":\"High Contrast Mode\",\"highContrastModeDescription\":\"Increases text/background contrast for better readability.\",\"enabledLabel\":\"Enabled\",\"disabledLabel\":\"Disabled\",\"personalDictionaryLabel\":\"Personal Dictionary\",\"personalDictionaryDescription\":\"Add words you use frequently that might be flagged as errors.\",\"addWordPlaceholder\":\"Enter a word...\",\"addWordButton\":\"Add Word\",\"deleteWordButtonAria\":\"Delete word {{word}}\",\"dictionaryEmptyPlaceholder\":\"Your dictionary is empty. Add some words!\",\"dictionaryImportExportLabel\":\"Import / Export Dictionary\",\"importDictionaryButton\":\"Import\",\"exportDictionaryButton\":\"Export\",\"dictionaryImportExportDescription\":\"Backup or share your personal dictionary as a JSON file.\",\"clearDictionaryForLanguageButton\":\"Clear {{language}} Dictionary\",\"clearDictionaryConfirmTitle\":\"Are you sure?\",\"clearDictionaryForLanguageConfirmDescription\":\"This will permanently delete all words from your personal dictionary for {{language}}. This action cannot be undone.\",\"confirmClearButton\":\"Yes, Clear Dictionary\",\"clearDictionaryWarning\":\"This action is irreversible.\",\"toastDictionaryWordAdded\":\"Word '{{word}}' added to dictionary.\",\"toastDictionaryWordExists\":\"Word '{{word}}' already exists in dictionary.\",\"toastDictionaryWordEmpty\":\"Cannot add an empty word.\",\"toastDictionaryWordDeleted\":\"Word '{{word}}' deleted from dictionary.\",\"toastDictionaryImportOverwriteSuccess\":\"{{count}} words imported, dictionary overwritten.\",\"toastDictionaryImportMergeSuccess\":\"{{count}} new words imported and merged.\",\"toastDictionaryImportInvalidFormat\":\"Invalid dictionary file format. Must be a JSON array of strings.\",\"toastDictionaryImportError\":\"Error importing dictionary file.\",\"toastDictionaryExportSuccess\":\"Dictionary exported successfully.\",\"toastDictionaryCleared\":\"Personal dictionary cleared.\",\"toastLanguageSwitched\":\"Writing language automatically switched to {{language}}.\",\"writingAssistanceTitle\":\"Writing Assistance\",\"writingAssistanceDescription\":\"Enhance your writing experience with LinguaFlow by customising how it assists you.\",\"yourLanguageProficiencyTitle\":\"Your Language Proficiency (for Primary Language)\",\"yourLanguageProficiencyDescription\":\"Receive meticulously tailored suggestions that align with your sophisticated understanding of language, ensuring a more nuanced approach to your writing.\",\"toneDetectionTitle\":\"Tone Detection\",\"toneDetectionDescription\":\"Delve into the emotional undertones of your text with our tone detection feature, which analyses your writing and offers insightful suggestions to refine and elevate your tone, making it resonate more effectively with your audience.\",\"nonNativeSupportTitle\":\"Support for Non-native Speakers\",\"nonNativeSupportDescription\":\"Benefit from specialised assistance aimed at non-native speakers, providing thoughtful guidance and practical tips to enhance your fluency and confidence in writing.\",\"advancedSettingsTitle\":\"Advanced Settings\",\"enableOfflineFunctionalityLabel\":\"Enable Offline Functionality\",\"enableOfflineFunctionalityDescription\":\"Basic features, including settings and the dictionary, are available offline. Key components will be included to ensure seamless program function.\",\"enableAutomaticLanguageDetectionLabel\":\"Enable Automatic Language Detection\",\"enableAutomaticLanguageDetectionDescription\":\"Automatically detect and switch the writing language as you type.\",\"dataManagementLabel\":\"Data Management\",\"resetAllSettingsLabel\":\"Reset All Settings\",\"resetAllSettingsDescription\":\"This will reset all customisations including theme, language, and feature settings to their defaults. This action cannot be undone.\",\"resetButtonLabel\":\"Reset\",\"resetAllSettingsConfirmTitle\":\"Are you sure you want to reset all settings?\",\"resetAllSettingsConfirmDescription\":\"All your personal settings, dictionary words, and preferences will be permanently deleted and reset to the application defaults. This action cannot be undone.\",\"confirmResetButton\":\"Yes, Reset Everything\",\"toastResetSuccess\":\"All settings have been reset to default. The application will now reload.\",\"dictionaryLanguageLabel\":\"Dictionary Language\",\"selectDictionaryLanguagePlaceholder\":\"Select language to view...\",\"dictionaryLanguageDescription\":\"View and manage the dictionary for a specific language.\",\"toastSuggestionDismissed\":\"Suggestion dismissed.\",\"dismissButton\":\"Dismiss\",\"correctButton\":\"Correct\",\"undoButton\":\"Undo\",\"redoButton\":\"Redo\",\"aiToolsButton\":\"Smart Synonyms\",\"wordToolkitTitle\":\"Synonyms Suggestion\",\"wordToolkitDescription\":\"Select a single word in the editor to get synonyms and hear its pronunciation.\",\"wordToolkitPlaceholder\":\"Select a single word in the main editor to get smart synonyms.\",\"selectedWordLabel\":\"Selected Word\",\"synonymsLabel\":\"Synonyms\",\"noSynonymsFound\":\"No synonyms found.\",\"applySynonymTooltip\":\"Replace word with '{{synonym}}'\",\"toastWordToolkitError\":\"Failed to get suggestions for the selected word.\",\"toastWordReplacedSuccess\":\"Word replaced with '{{word}}'.\",\"wordToolkitPopoverDescription\":\"Get synonyms or hear the word's pronunciation.\",\"spellingAndPronunciationLabel\":\"Pronunciation\",\"pronounceButton\":\"Pronounce Word\",\"toastPronunciationError\":\"Failed to generate audio pronunciation.\",\"toastEmptyText\":\"Please enter some text first.\"}"));}}),
"[project]/src/locales/ar.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"appName\":\"LinguaFlow\",\"appDescription\":\"مُصحح نحوي ومساعد كتابة\",\"editorTitle\":\"المحرر\",\"rephraseSelectionButton\":\"إعادة صياغة التحديد\",\"writingStatsTitle\":\"إحصائيات الكتابة\",\"wordCountLabel\":\"عدد الكلمات\",\"charCountLabel\":\"عدد الأحرف\",\"writingScoreLabel\":\"درجة الكتابة\",\"writingScoreUnit\":\"/ 100\",\"writingModeLabel\":\"وضع الكتابة\",\"selectWritingModePlaceholder\":\"اختر وضع الكتابة\",\"formalWritingMode\":\"رسمي\",\"casualWritingMode\":\"غير رسمي\",\"professionalWritingMode\":\"احترافي\",\"creativeWritingMode\":\"إبداعي\",\"technicalWritingMode\":\"تقني\",\"academicWritingMode\":\"أكاديمي\",\"businessWritingMode\":\"عمل\",\"aiToneAnalysisAccordionTitle\":\"تحليل النبرة\",\"aiToneAnalysisTitle\":\"تحليل النبرة\",\"aiToneAnalysisDescription\":\"احصل على ملاحظات حول رسمية وثقة كتابتك.\",\"analyzeToneButton\":\"تحليل نبرة النص\",\"formalityLabel\":\"الرسمية\",\"confidenceLabel\":\"الثقة\",\"feedbackLabel\":\"الملاحظات\",\"writeSomeTextToAnalyzePlaceholder\":\"اكتب نصًا في المحرر لتحليل نبرته.\",\"aiTextGenerationAccordionTitle\":\"إنشاء المحتوى بالذكاء الاصطناعي\",\"aiTextGenerationTitle\":\"مولد المحتوى بالذكاء الاصطناعي\",\"aiTextGenerationDescription\":\"قم بإنشاء محتوى بناءً على طلبك.\",\"yourPromptLabel\":\"طلبك\",\"promptPlaceholder\":\"مثال: اكتب قصة قصيرة عن روبوت يكتشف الموسيقى\",\"generatedTextLabel\":\"النص المُنشأ\",\"generateTextButton\":\"إنشاء نص\",\"settingsAccordionTitle\":\"الإعدادات\",\"settingsTitle\":\"الإعدادات\",\"settingsDescription\":\"قم بتخصيص تجربتك في LinguaFlow.\",\"themeLabel\":\"المظهر\",\"switchToLightMode\":\"التبديل إلى الوضع الفاتح\",\"switchToDarkMode\":\"التبديل إلى الوضع الداكن\",\"languageLabel\":\"اللغة\",\"selectLanguagePlaceholder\":\"اختر اللغة\",\"englishUSLanguage\":\"English (US)\",\"englishUKLanguage\":\"English (UK)\",\"arabicLanguage\":\"العربية (Arabic)\",\"turkishLanguage\":\"Türkçe (Turkish)\",\"spanishLanguage\":\"Español (Spanish)\",\"germanLanguage\":\"Deutsch (German)\",\"frenchLanguage\":\"Français (French)\",\"dutchLanguage\":\"Nederlands (Dutch)\",\"italianLanguage\":\"Italiano (Italian)\",\"startWritingPlaceholder\":\"ابدأ الكتابة هنا...\",\"rephrasePopoverTitle\":\"إعادة صياغة النص\",\"rephrasePopoverDescription\":\"راجع الاقتراح للنص المحدد.\",\"originalTextLabel\":\"الأصلي\",\"suggestionTextLabel\":\"الاقتراح\",\"rephraseWaitMessage\":\"انقر \\\"إعادة صياغة\\\" أو انتظر الاقتراح.\",\"applyButton\":\"تطبيق\",\"cancelButton\":\"إلغاء\",\"siteHeaderTitle\":\"LinguaFlow\",\"footerText\":\"تم إنشاؤه بواسطة المهندس أحمد 2025©. جميع الحقوق محفوظة®. نقدر دعمكم و شكركم\",\"toastInputRequiredTitle\":\"الإدخال مطلوب\",\"toastEditorEmptyError\":\"المحرر فارغ. يرجى كتابة بعض النصوص لتحليلها.\",\"toastPromptRequiredError\":\"يرجى إدخال طلب لإنشاء نص.\",\"toastSuccessTitle\":\"نجاح\",\"toastErrorTitle\":\"خطأ\",\"toastInfoTitle\":\"معلومة\",\"toastTextGeneratedSuccess\":\"تم إنشاء النص بنجاح.\",\"toastTextGenerationError\":\"فشل إنشاء النص. يرجى المحاولة مرة أخرى.\",\"toastToneAnalysisSuccess\":\"اكتمل تحليل النبرة.\",\"toastToneAnalysisError\":\"فشل تحليل النبرة. يرجى المحاولة مرة أخرى.\",\"toastNothingToRephraseError\":\"لا يوجد شيء لإعادة صياغته\",\"toastSelectTextToRephraseError\":\"يرجى تحديد بعض النصوص في المحرر.\",\"toastSuggestionReady\":\"الاقتراح جاهز\",\"toastRephraseError\":\"فشل في إعادة صياغة النص. يرجى المحاولة مرة أخرى.\",\"toastFileUploadedSuccess\":\"تم تحميل محتوى الملف إلى المحرر.\",\"toastFileTypeNotSupportedError\":\"نوع الملف غير مدعوم. يرجى رفع ملف {{fileType}}.\",\"plagiarismDetectionAccordionTitle\":\"كشف السرقة الأدبية\",\"plagiarismDetectionTitle\":\"كشف السرقة الأدبية\",\"plagiarismDetectionSettingsTitle\":\"كشف السرقة الأدبية\",\"plagiarismDetectionDescription\":\"احمِ نزاهتك باستخدام أداة كشف السرقة الأدبية الخاصة بنا، المصممة لفحص المحتوى الخاص بك بدقة بحثًا عن أوجه التشابه غير المقصودة مع المؤلفات الموجودة، مما يساعدك في الحفاظ على الأصالة في كتاباتك.\",\"detectPlagiarismButton\":\"كشف السرقة الأدبية\",\"originalityScoreLabel\":\"درجة الأصالة\",\"plagiarismReportLabel\":\"تقرير التحليل\",\"potentialSourcesFoundLabel\":\"تم العثور على مصادر محتملة\",\"originalSourceLabel\":\"المصدر الأصلي\",\"similarityScoreLabel\":\"درجة التشابه\",\"toastPlagiarismDetectionSuccess\":\"اكتمل فحص السرقة الأدبية.\",\"toastPlagiarismDetectionError\":\"فشل فحص السرقة الأدبية. يرجى المحاولة مرة أخرى.\",\"writeSomeTextToDetectPlagiarismPlaceholder\":\"اكتب نصًا في المحرر للتحقق من السرقة الأدبية.\",\"aiWritingDetectionAccordionTitle\":\"كشّاف الكتابة\",\"aiWritingDetectionTitle\":\"كشّاف الكتابة\",\"aiWritingDetectionDescription\":\"قدّر احتمالية أن يكون نصك قد تم إنشاؤه بواسطة الذكاء الاصطناعي.\",\"detectAiWritingButton\":\"كشف نوع الكتابة\",\"probabilityAIWrittenLabel\":\"احتمالية الكتابة بواسطة الذكاء الاصطناعي\",\"aiWritingDetectionSummaryLabel\":\"ملخص التحليل\",\"toastAiWritingDetectionSuccess\":\"اكتمل كشف الكتابة بالذكاء الاصطناعي.\",\"toastAiWritingDetectionError\":\"فشل كشف الكتابة بالذكاء الاصطناعي. يرجى المحاولة مرة أخرى.\",\"writeSomeTextToDetectAiWritingPlaceholder\":\"اكتب نصًا في المحرر للتحقق من تأليف الذكاء الاصطناعي.\",\"writingSuggestionsTitle\":\"اقتراحات الكتابة\",\"analyzingTextDescription\":\"يقوم الذكاء الاصطناعي بتحليل نصك بحثًا عن اقتراحات...\",\"suggestionsFoundDescription\":\"تم العثور على {{count}} اقتراح. راجعها أدناه.\",\"noSuggestionsFoundDescription\":\"لم يتم العثور على اقتراحات فورية. استمر في الكتابة أو حاول إعادة الصياغة.\",\"startTypingForSuggestionsDescription\":\"ابدأ الكتابة للحصول على اقتراحات كتابة مدعومة بالذكاء الاصطناعي.\",\"suggestionTypeSpelling\":\"إملائي\",\"suggestionTypeGrammar\":\"نحوي\",\"suggestionTypeRewrite\":\"إعادة كتابة\",\"suggestionTypeStyle\":\"أسلوبي\",\"suggestionTypeUnknown\":\"اقتراح\",\"suggestionLabel\":\"يقترح\",\"applySuggestionButton\":\"تطبيق الاقتراح\",\"suggestionExplanationTooltip\":\"عرض الشرح\",\"toastTextAnalysisError\":\"فشل الحصول على اقتراحات الكتابة. يرجى المحاولة مرة أخرى.\",\"toastSuggestionAppliedSuccess\":\"تم تطبيق الاقتراح.\",\"toastSuggestionApplyError\":\"تعذر تطبيق الاقتراح. ربما تغير النص الأصلي.\",\"humanizeAiTextAccordionTitle\":\"تحويل النص الاصطناعي إلى كتابة بشرية\",\"humanizeAiTextTitle\":\"تحويل النص الاصطناعي إلى كتابة بشرية\",\"humanizeAiTextDescription\":\"إعادة كتابة النص الذي تم إنشاؤه بواسطة الذكاء الاصطناعي ليبدو أكثر شبهاً بالكتابة البشرية.\",\"humanizeTextButton\":\"تحويل النص إلى كتابة بشرية\",\"humanizedTextLabel\":\"النص المحول إلى كتابة بشرية\",\"toastHumanizeTextSuccess\":\"تم تحويل النص إلى كتابة بشرية بنجاح.\",\"toastHumanizeTextError\":\"فشل تحويل النص إلى كتابة بشرية. يرجى المحاولة مرة أخرى.\",\"writeSomeTextToHumanizePlaceholder\":\"اكتب أو الصق النص الذي تم إنشاؤه بواسطة الذكاء الاصطناعي في المحرر لتحويله إلى كتابة بشرية.\",\"clearEditorButton\":\"مسح\",\"clearEditorButtonAriaLabel\":\"مسح كل النص من المحرر\",\"toastEditorClearedSuccess\":\"تم مسح محتوى المحرر.\",\"generationHistoryTitle\":\"سجل الإنشاء\",\"noGenerationsYetPlaceholder\":\"لا توجد إنشاءات بعد. قم بإنشاء بعض النصوص لرؤيتها هنا.\",\"promptLabel\":\"الطلب\",\"outputLabel\":\"الناتج\",\"useThisPromptButton\":\"استخدام هذا الطلب\",\"copyOutputButton\":\"نسخ الناتج\",\"toastPromptRestoredSuccess\":\"تمت استعادة الطلب إلى حقل الإدخال.\",\"toastTextCopiedSuccess\":\"تم نسخ النص إلى الحافظة.\",\"toastTextCopyError\":\"فشل نسخ النص إلى الحافظة.\",\"insertIntoEditorButton\":\"إدراج في المحرر\",\"insertIntoEditorButtonTooltip\":\"إلحاق النص المُنشأ بالمحرر\",\"toastTextInsertedSuccess\":\"تم إدراج النص المُنشأ في المحرر.\",\"copyEditorButton\":\"نسخ النص\",\"copyEditorButtonAriaLabel\":\"نسخ كل النص من المحرر\",\"toastEditorContentCopiedSuccess\":\"تم نسخ محتوى المحرر إلى الحافظة.\",\"toastEditorContentCopyError\":\"فشل نسخ محتوى المحرر إلى الحافظة.\",\"toastEditorEmptyForCopyError\":\"المحرر فارغ. لا يوجد شيء لنسخه.\",\"recordVoiceButtonStart\":\"تسجيل صوت\",\"recordVoiceButtonStop\":\"إيقاف التسجيل\",\"recordVoiceButtonAriaLabelStart\":\"بدء تسجيل الصوت لتحويله إلى نص\",\"recordVoiceButtonAriaLabelStop\":\"إيقاف تسجيل الصوت\",\"toastRecordingStarted\":\"بدأ التسجيل. تحدث في الميكروفون.\",\"toastRecordingStoppedNoTranscript\":\"توقف التسجيل. لم يتم تحويل أي كلام.\",\"toastSpeechTranscribedAndAppended\":\"تم تحويل الكلام وإضافته إلى المحرر.\",\"toastSpeechRecognitionNotSupported\":\"متصفحك لا يدعم التعرف على الكلام.\",\"toastMicrophonePermissionDenied\":\"تم رفض إذن الميكروفون. يرجى تمكينه في إعدادات المتصفح وتحديث الصفحة.\",\"toastSpeechNoSpeechDetected\":\"لم يتم اكتشاف أي كلام. حاول مرة اخرى.\",\"toastSpeechAudioCaptureError\":\"خطأ في التقاط الصوت. يرجى التحقق من الميكروفون.\",\"toastSpeechNetworkError\":\"خطأ في الشبكة أثناء التعرف على الكلام. تحقق من اتصالك.\",\"toastSpeechRecognitionError\":\"خطأ في التعرف على الكلام: {{error}}\",\"toastSpeechServiceNotAllowed\":\"خدمة التعرف على الكلام غير مسموح بها أو غير متوفرة. يرجى المحاولة مرة أخرى لاحقًا.\",\"toastSpeechLanguageNotSupportedError\":\"اللغة المحددة غير مدعومة للتعرف على الكلام بواسطة متصفحك.\",\"helpTitle\":\"مساعدة\",\"helpPanelTitle\":\"كيفية استخدام LinguaFlow\",\"helpPanelDescription\":\"ابدأ باستخدام ميزات LinguaFlow.\",\"helpPanelIntro\":\"مرحبًا بك في LinguaFlow! سيساعدك هذا الدليل على التنقل واستخدام أدوات مساعدة الكتابة القوية في التطبيق.\",\"helpEditorTitle\":\"المحرر\",\"helpAiToolsTitle\":\"لوحة أدوات الذكاء الاصطناعي\",\"helpLanguageSettingsTitle\":\"إعدادات اللغة\",\"helpAppearanceSettingsTitle\":\"إعدادات المظهر\",\"helpDictionarySettingsTitle\":\"إعدادات القاموس\",\"helpFeatureSettingsTitle\":\"إعدادات الميزات\",\"helpWritingAidSettingsTitle\":\"إعدادات مساعدة الكتابة\",\"helpAdvancedSettingsTitle\":\"إعدادات متقدمة\",\"helpEditorDescription\":\"المحرر هو مساحة عملك الرئيسية. <br/><br/> <b>- اقتراحات في الوقت الفعلي:</b> أثناء الكتابة، يقوم التطبيق تلقائيًا بفحص النص الخاص بك ويسطر المشكلات المحتملة. انقر فوق مقطع مميز لرؤية نافذة منبثقة للتصحيح. <br/> <b>- أدوات الذكاء الاصطناعي عند التحديد:</b> حدد أي جزء من النص لإظهار زر <b>المرادفات الذكية</b>. إذا حددت كلمة واحدة، فستحصل على مرادفات ودليل نطق. إذا حددت عبارة أطول، فستحصل على اقتراح إعادة صياغة مدعوم بالذكاء الاصطناعي. <br/> <b>- شريط أدوات التنسيق:</b> في الجزء العلوي من المحرر، ستجد أدوات للتراجع/الإعادة، وتغيير الخطوط، وتطبيق التنسيق مثل التعداد النقطي والقوائم المرقمة ومحاذاة النص.\",\"helpAiToolsDescription\":\"توفر اللوحات الموجودة على اليسار واليمين إمكانيات قوية للذكاء الاصطناعي. <br/><br/> <b>- أدوات الكتابة (يسار):</b> هنا يمكنك تغيير <b>وضع الكتابة</b> للتأثير على أسلوب الذكاء الاصطناعي، أو <b>استيراد مستند</b>، أو استخدام <b>معيد كتابة الذكاء الاصطناعي</b> لإعادة صياغة النص بأكمله. يمكنك أيضًا استخدام أدوات <b>لإضفاء الطابع الإنساني على نص الذكاء الاصطناعي</b>، والتحقق من <b>كتابة الذكاء الاصطناعي</b>، واكتشاف <b>الانتحال</b>. <br/> <b>- أدوات التحليل (يمين):</b> يعرض هذا العمود <b>إحصائيات الكتابة</b> في الوقت الفعلي، ويوفر <b>محلل نغمة</b> لنصك، ويتضمن <b>مولد محتوى الذكاء الاصطناعي</b> لإنشاء نص جديد من مطالبة. يتم حفظ سجل التوليد الخاص بك هنا لسهولة إعادة الاستخدام.\",\"helpLanguageSettingsDescription\":\"تكوين <b>لغة واجهة المستخدم</b> لواجهة التطبيق و<b>لغة الكتابة الأساسية</b> للتحليل. بالنسبة لبعض اللغات، يمكنك أيضًا تحديد <b>لهجة إقليمية</b>. قم بتمكين <b>الكشف التلقائي عن اللغة</b> لجعل التطبيق يغير لغات الكتابة أثناء الكتابة.\",\"helpAppearanceSettingsDescription\":\"تخصيص شكل ومظهر التطبيق. اختر <b>مظهرًا</b> (فاتح، داكن، أو النظام)، واضبط <b>حجم الخط</b> العام، وقم بتبديل <b>وضع التباين العالي</b> لتحسين القراءة.\",\"helpDictionarySettingsDescription\":\"إدارة قواميسك الشخصية للغات مختلفة. <b>أضف</b> الكلمات التي تستخدمها كثيرًا ولكن قد يتم تمييزها كأخطاء إملائية (مثل الأسماء أو المصطلحات الفنية). يمكنك أيضًا <b>استيراد</b> أو <b>تصدير</b> قائمة قاموسك كملف JSON، أو <b>مسح</b> القاموس للغة معينة.\",\"helpFeatureSettingsDescription\":\"ضبط سلوك ميزات الذكاء الاصطناعي والتصحيح التلقائي المحددة. هنا يمكنك تشغيل/إيقاف تشغيل أدوات الذكاء الاصطناعي التوليدية المختلفة، وسلوكيات التصحيح التلقائي، ووظائف التحقق الأساسية في الوقت الفعلي لتتناسب مع سير عملك.\",\"helpWritingAidSettingsDescription\":\"تخصيص كيفية مساعدتك من قبل الذكاء الاصطناعي. حدد <b>كفاءتك اللغوية</b> للحصول على اقتراحات مصممة خصيصًا لمستوى مهارتك. يمكنك أيضًا تمكين أو تعطيل ميزات مثل <b>اكتشاف النغمة</b> و<b>اكتشاف الانتحال</b> والدعم المتخصص <b>للمتحدثين غير الأصليين</b>.\",\"helpAdvancedSettingsDescription\":\"التحكم في السلوكيات التشغيلية الأساسية. قم بتمكين <b>الوظائف دون اتصال</b> لاستخدام الميزات الأساسية بدون اتصال بالإنترنت. إذا كنت ترغب في البدء من جديد، يمكنك <b>إعادة ضبط جميع الإعدادات</b> لاستعادة التطبيق إلى إعداداته الافتراضية الأصلية (لا يمكن التراجع عن هذا الإجراء).\",\"helpPanelTip\":\"جرب أدوات وإعدادات مختلفة للعثور على أفضل ما يناسب أسلوب كتابتك واحتياجاتك!\",\"Write Tools\":\"أدوات الكتابة\",\"Import Document\":\"استيراد مستند\",\"Quick Action\":\"إجراء سريع\",\"Tone Analyzer\":\"محلل النغمة\",\"aiRewriteAccordionTitle\":\"إعادة الكتابة بالذكاء الاصطناعي\",\"aiRewriteTitle\":\"إعادة الكتابة بالذكاء الاصطناعي\",\"aiRewriteDescription\":\"إعادة كتابة محتوى المحرر بالكامل لتحسين الوضوح والأسلوب.\",\"rewriteEditorContentButton\":\"إعادة كتابة محتوى المحرر\",\"rewrittenTextLabel\":\"النص المعاد كتابته\",\"applyToEditorButton\":\"تطبيق على المحرر\",\"toastRewriteSuccess\":\"تمت إعادة كتابة محتوى المحرر بنجاح.\",\"toastRewriteError\":\"فشل في إعادة كتابة محتوى المحرر. يرجى المحاولة مرة أخرى.\",\"writeSomeTextToRewritePlaceholder\":\"اكتب نصًا في المحرر لإعادة كتابته.\",\"dropzoneInstruction\":\"أسقط الملفات هنا أو تصفح\",\"toastFileImportSuccessTitle\":\"تم استيراد الملف\",\"toastFileImportSuccessMessage\":\"تم تحميل محتوى المستند.\",\"toastFileImportErrorTitle\":\"خطأ في الاستيراد\",\"toastFileImportErrorMessage\":\"تعذر قراءة محتوى الملف. يرجى التأكد من أنه ملف .txt صالح.\",\"toastInvalidFileTypeMessage\":\"نوع الملف غير صالح. يتم قبول ملفات .txt فقط.\",\"dropzoneAriaLabel\":\"منطقة إفلات استيراد المستندات: انقر أو اسحب وأفلت ملف .txt للتحميل.\",\"featuresLabel\":\"الميزات\",\"featureSettingsDescription\":\"تخصيص وظائف ميزات مساعدة الكتابة المحددة.\",\"appearanceLabel\":\"المظهر\",\"writingAidLabel\":\"مساعدة الكتابة\",\"dictionaryLabel\":\"القاموس\",\"dictionarySettingsDescription\":\"إضافة كلمات مخصصة أو إدارة القواميس الشخصية. (عنصر نائب)\",\"advancedSettingsLabel\":\"متقدم\",\"advancedSettingsDescription\":\"الوصول إلى خيارات التكوين المتقدمة. استخدم بحذر. (عنصر نائب)\",\"uiLanguageLabel\":\"لغة الواجهة\",\"selectUiLanguagePlaceholder\":\"اختر لغة الواجهة...\",\"uiLanguageDescription\":\"يغير لغة واجهة التطبيق.\",\"writingLanguageLabel\":\"لغة الكتابة الأساسية\",\"selectWritingLanguagePlaceholder\":\"اختر لغة الكتابة...\",\"writingLanguageDescription\":\"يضبط اللغة الأساسية لتحليل وإنشاء الذكاء الاصطناعي.\",\"regionalDialectLabel\":\"اللهجة الإقليمية\",\"selectRegionalDialectPlaceholder\":\"اختر اللهجة...\",\"regionalDialectDescription\":\"يحدد التباين الإقليمي للغة الكتابة المختارة.\",\"languageProficiencyLabel\":\"مستوى إتقان اللغة\",\"selectProficiencyPlaceholder\":\"اختر مستوى الإتقان...\",\"languageProficiencyDescription\":\"يساعد الذكاء الاصطناعي على تخصيص الاقتراحات لمستوى مهارتك اللغوية.\",\"proficiencyNative\":\"أصلي\",\"proficiencyAdvanced\":\"متقدم (C1/C2)\",\"proficiencyIntermediate\":\"متوسط (B1/B2)\",\"proficiencyBeginner\":\"مبتدئ (A1/A2)\",\"languageEnglishGeneral\":\"الإنجليزية\",\"languageSpanishGeneral\":\"الإسبانية\",\"languageFrenchGeneral\":\"الفرنسية\",\"languageGermanGeneral\":\"الألمانية\",\"languageItalianGeneral\":\"الإيطالية\",\"languageDutchGeneral\":\"الهولندية\",\"languageArabicGeneral\":\"العربية\",\"arabicSyriaLanguage\":\"العربية (سوريا)\",\"arabicSaudiArabiaLanguage\":\"العربية(السعودية)\",\"arabicEgyptLanguage\":\"العربية(المصرية)\",\"languageTurkishGeneral\":\"التركية\",\"spanishSpainLanguage\":\"الإسبانية (إسبانيا)\",\"spanishMexicoLanguage\":\"الإسبانية (المكسيك)\",\"themeLight\":\"فاتح\",\"themeDark\":\"داكن\",\"themeSystem\":\"افتراضي النظام\",\"selectThemePlaceholder\":\"اختر المظهر...\",\"themeDescription\":\"اختر המظهر المرئي للتطبيق.\",\"fontSizeLabel\":\"حجم الخط\",\"selectFontSizePlaceholder\":\"اختر حجم الخط...\",\"fontSizeSmall\":\"صغير\",\"fontSizeMedium\":\"متوسط\",\"fontSizeLarge\":\"كبير\",\"fontSizeDescription\":\"اضبط حجم النص في جميع أنحاء التطبيق.\",\"highContrastModeLabel\":\"وضع التباين العالي\",\"highContrastModeDescription\":\"يزيد من تباين النص/الخلفية لتحسين القراءة.\",\"enabledLabel\":\"مفعل\",\"disabledLabel\":\"معطل\",\"personalDictionaryLabel\":\"القاموس الشخصي\",\"personalDictionaryDescription\":\"أضف الكلمات التي تستخدمها بشكل متكرر والتي قد يتم تمييزها كأخطاء.\",\"addWordPlaceholder\":\"أدخل كلمة...\",\"addWordButton\":\"إضافة كلمة\",\"deleteWordButtonAria\":\"حذف كلمة {{word}}\",\"dictionaryEmptyPlaceholder\":\"قاموسك فارغ. أضف بعض الكلمات!\",\"dictionaryImportExportLabel\":\"استيراد / تصدير القاموس\",\"importDictionaryButton\":\"استيراد\",\"exportDictionaryButton\":\"تصدير\",\"dictionaryImportExportDescription\":\"قم بعمل نسخة احتياطية أو شارك قاموسك الشخصي كملف JSON.\",\"clearDictionaryForLanguageButton\":\"مسح قاموس {{language}}\",\"clearDictionaryConfirmTitle\":\"هل أنت متأكد؟\",\"clearDictionaryForLanguageConfirmDescription\":\"سيؤدي هذا إلى حذف جميع الكلمات من قاموسك الشخصي للغة {{language}} بشكل دائم. لا يمكن التراجع عن هذا الإجراء.\",\"confirmClearButton\":\"نعم، مسح القاموس\",\"clearDictionaryWarning\":\"لا يمكن التراجع عن هذا الإجراء.\",\"toastDictionaryWordAdded\":\"تمت إضافة كلمة '{{word}}' إلى القاموس.\",\"toastDictionaryWordExists\":\"كلمة '{{word}}' موجودة بالفعل في القاموس.\",\"toastDictionaryWordEmpty\":\"لا يمكن إضافة كلمة فارغة.\",\"toastDictionaryWordDeleted\":\"تم حذف كلمة '{{word}}' من القاموس.\",\"toastDictionaryImportOverwriteSuccess\":\"تم استيراد {{count}} كلمة، وتمت الكتابة فوق القاموس.\",\"toastDictionaryImportMergeSuccess\":\"تم استيراد {{count}} كلمة جديدة ودمجها.\",\"toastDictionaryImportInvalidFormat\":\"تنسيق ملف القاموس غير صالح. يجب أن يكون مصفوفة JSON من السلاسل النصية.\",\"toastDictionaryImportError\":\"خطأ في استيراد ملف القاموس.\",\"toastDictionaryExportSuccess\":\"تم تصدير القاموس بنجاح.\",\"toastDictionaryCleared\":\"تم مسح القاموس الشخصي.\",\"toastLanguageSwitched\":\"تم تبديل لغة الكتابة تلقائيًا إلى {{language}}.\",\"writingAssistanceTitle\":\"مساعدة في الكتابة\",\"writingAssistanceDescription\":\"عزز تجربة كتابتك مع LinguaFlow من خلال تخصيص كيفية مساعدتك.\",\"yourLanguageProficiencyTitle\":\"كفاءتك اللغوية (للغة الأساسية)\",\"yourLanguageProficiencyDescription\":\"احصل على اقتراحات مصممة بدقة تتوافق مع فهمك المتطور للغة، مما يضمن نهجًا أكثر دقة في كتابتك.\",\"toneDetectionTitle\":\"كشف النغمة\",\"toneDetectionDescription\":\"تعمق في النغمات العاطفية لنصك باستخدام ميزة كشف النغمة لدينا، التي تحلل كتابتك وتقدم اقتراحات ثاقبة لتحسين ورفع نبرتك، مما يجعلها تتناغم بشكل أكثر فعالية مع جمهورك.\",\"nonNativeSupportTitle\":\"دعم المتحدثين غير الأصليين\",\"nonNativeSupportDescription\":\"استفد من المساعدة المتخصصة التي تستهدف المتحدثين غير الأصليين، والتي توفر إرشادات مدروسة ونصائح عملية لتعزيز طلاقتك وثقتك في الكتابة.\",\"advancedSettingsTitle\":\"إعدادات متقدمة\",\"enableOfflineFunctionalityLabel\":\"تمكين الوظائف دون اتصال\",\"enableOfflineFunctionalityDescription\":\"الميزات الأساسية، بما في ذلك الإعدادات والقاموس، متاحة دون اتصال. سيتم تضمين المكونات الرئيسية لضمان عمل البرنامج بسلاسة.\",\"enableAutomaticLanguageDetectionLabel\":\"تمكين الكشف التلقائي عن اللغة\",\"enableAutomaticLanguageDetectionDescription\":\"الكشف التلقائي عن لغة الكتابة وتبديلها أثناء الكتابة.\",\"dataManagementLabel\":\"إدارة البيانات\",\"resetAllSettingsLabel\":\"إعادة ضبط جميع الإعدادات\",\"resetAllSettingsDescription\":\"سيؤدي هذا إلى إعادة ضبط جميع التخصيصات بما في ذلك السمة واللغة وإعدادات الميزات إلى قيمها الافتراضية. لا يمكن التراجع عن هذا الإجراء.\",\"resetButtonLabel\":\"إعادة ضبط\",\"resetAllSettingsConfirmTitle\":\"هل أنت متأكد من أنك تريد إعادة ضبط جميع الإعدادات؟\",\"resetAllSettingsConfirmDescription\":\"سيتم حذف جميع إعداداتك الشخصية وكلمات القاموس والتفضيلات بشكل دائم وإعادتها إلى الإعدادات الافتراضية للتطبيق. لا يمكن التراجع عن هذا الإجراء.\",\"confirmResetButton\":\"نعم، أعد ضبط كل شيء\",\"toastResetSuccess\":\"تمت إعادة ضبط جميع الإعدادات إلى الوضع الافتراضي. سيتم الآن إعادة تحميل التطبيق.\",\"dictionaryLanguageLabel\":\"لغة القاموس\",\"selectDictionaryLanguagePlaceholder\":\"اختر لغة لعرض القاموس...\",\"dictionaryLanguageDescription\":\"عرض وإدارة القاموس للغة معينة.\",\"toastSuggestionDismissed\":\"تم تجاهل الاقتراح.\",\"dismissButton\":\"تجاهل\",\"correctButton\":\"تصحيح\",\"undoButton\":\"تراجع\",\"redoButton\":\"إعادة\",\"aiToolsButton\":\"المرادفات الذكية\",\"wordToolkitTitle\":\"اقتراح المرادفات\",\"wordToolkitDescription\":\"اختر كلمة واحدة في المحرر للحصول على مرادفات وسماع نطقها.\",\"wordToolkitPlaceholder\":\"للحصول على مرادفات ذكية، اختر كلمة واحدة في المحرر الرئيسي.\",\"selectedWordLabel\":\"الكلمة المختارة\",\"synonymsLabel\":\"مرادفات\",\"noSynonymsFound\":\"لم يتم العثور على مرادفات.\",\"applySynonymTooltip\":\"استبدال الكلمة بـ '{{synonym}}'\",\"toastWordToolkitError\":\"فشل في الحصول على اقتراحات للكلمة المحددة.\",\"toastWordReplacedSuccess\":\"تم استبدال الكلمة بـ '{{word}}'.\",\"wordToolkitPopoverDescription\":\"احصل على مرادفات أو استمع إلى نطق الكلمة.\",\"spellingAndPronunciationLabel\":\"النطق\",\"pronounceButton\":\"نطق الكلمة\",\"toastPronunciationError\":\"فشل في إنشاء النطق الصوتي.\"}"));}}),
"[project]/src/locales/tr.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"appName\":\"LinguaFlow\",\"appDescription\":\"Dilbilgisi Düzeltme ve Yazma Asistanı\",\"editorTitle\":\"Düzenleyici\",\"rephraseSelectionButton\":\"Seçimi Yeniden İfade Et\",\"writingStatsTitle\":\"Yazma İstatistikleri\",\"wordCountLabel\":\"Kelime Sayısı\",\"charCountLabel\":\"Karakter Sayısı\",\"writingScoreLabel\":\"Yazma Puanı\",\"writingScoreUnit\":\"/ 100\",\"writingModeLabel\":\"Yazma Modu\",\"selectWritingModePlaceholder\":\"Yazma modunu seçin\",\"formalWritingMode\":\"Resmi\",\"casualWritingMode\":\"Günlük\",\"professionalWritingMode\":\"Profesyonel\",\"creativeWritingMode\":\"Yaratıcı\",\"technicalWritingMode\":\"Teknik\",\"academicWritingMode\":\"Akademik\",\"businessWritingMode\":\"İş\",\"aiToneAnalysisAccordionTitle\":\"Ton Analizi\",\"aiToneAnalysisTitle\":\"Ton Analizi\",\"aiToneAnalysisDescription\":\"Yazınızın resmiyeti ve güveni hakkında geri bildirim alın.\",\"analyzeToneButton\":\"Metninin Tonunu Analiz Et\",\"formalityLabel\":\"Resmiyet\",\"confidenceLabel\":\"Güven\",\"feedbackLabel\":\"Geri Bildirim\",\"writeSomeTextToAnalyzePlaceholder\":\"Tonunu analiz etmek için düzenleyiciye biraz metin yazın.\",\"aiTextGenerationAccordionTitle\":\"Yapay Zeka İçerik Üretimi\",\"aiTextGenerationTitle\":\"Yapay Zeka İçerik Üretici\",\"aiTextGenerationDescription\":\"İsteminize göre içerik üretin.\",\"yourPromptLabel\":\"İsteminiz\",\"promptPlaceholder\":\"örneğin, Müziği keşfeden bir robot hakkında kısa bir hikaye yazın\",\"generatedTextLabel\":\"Üretilen Metin\",\"generateTextButton\":\"Metin Üret\",\"settingsAccordionTitle\":\"Ayarlar\",\"settingsTitle\":\"Ayarlar\",\"settingsDescription\":\"LinguaFlow deneyiminizi özelleştirin.\",\"themeLabel\":\"Tema\",\"switchToLightMode\":\"Açık Moda Geç\",\"switchToDarkMode\":\"Koyu Moda Geç\",\"languageLabel\":\"Dil\",\"selectLanguagePlaceholder\":\"Dil seçin\",\"englishUSLanguage\":\"İngilizce (ABD)\",\"englishUKLanguage\":\"İngilizce (BK)\",\"arabicLanguage\":\"العربية (Arabic)\",\"turkishLanguage\":\"Türkçe (Turkish)\",\"spanishLanguage\":\"Español (Spanish)\",\"germanLanguage\":\"Deutsch (German)\",\"frenchLanguage\":\"Français (French)\",\"dutchLanguage\":\"Nederlands (Dutch)\",\"italianLanguage\":\"Italiano (Italian)\",\"startWritingPlaceholder\":\"Buraya yazmaya başlayın...\",\"rephrasePopoverTitle\":\"Metni Yeniden İfade Et\",\"rephrasePopoverDescription\":\"Seçili metniniz için öneriyi inceleyin.\",\"originalTextLabel\":\"Orijinal\",\"suggestionTextLabel\":\"Öneri\",\"rephraseWaitMessage\":\"\\\"Yeniden İfade Et\\\"e tıklayın veya öneriyi bekleyin.\",\"applyButton\":\"Uygula\",\"cancelButton\":\"İptal\",\"siteHeaderTitle\":\"LinguaFlow\",\"footerText\":\"Müh AZA7 tarafından oluşturuldu©2025. Tüm hakları saklıdır®. Desteğiniz ve takdiriniz büyük takdir görmektedir.\",\"toastInputRequiredTitle\":\"Giriş Gerekli\",\"toastEditorEmptyError\":\"Düzenleyici boş. Lütfen analiz etmek için biraz metin yazın.\",\"toastPromptRequiredError\":\"Lütfen metin üretmek için bir istem girin.\",\"toastSuccessTitle\":\"Başarılı\",\"toastErrorTitle\":\"Hata\",\"toastInfoTitle\":\"Bilgi\",\"toastTextGeneratedSuccess\":\"Metin başarıyla üretildi.\",\"toastTextGenerationError\":\"Metin üretilemedi. Lütfen tekrar deneyin.\",\"toastToneAnalysisSuccess\":\"Ton analizi tamamlandı.\",\"toastToneAnalysisError\":\"Ton analizi yapılamadı. Lütfen tekrar deneyin.\",\"toastNothingToRephraseError\":\"Yeniden ifade edilecek bir şey yok\",\"toastSelectTextToRephraseError\":\"Lütfen düzenleyicide biraz metin seçin.\",\"toastSuggestionReady\":\"Öneri hazır\",\"toastRephraseError\":\"Metin yeniden ifade edilemedi. Lütfen tekrar deneyin.\",\"toastFileUploadedSuccess\":\"Dosya içeriği düzenleyiciye yüklendi.\",\"toastFileTypeNotSupportedError\":\"Dosya türü desteklenmiyor. Lütfen bir {{fileType}} dosyası yükleyin.\",\"plagiarismDetectionAccordionTitle\":\"İntihal Tespiti\",\"plagiarismDetectionTitle\":\"İntihal Tespiti\",\"plagiarismDetectionSettingsTitle\":\"İntihal Tespiti\",\"plagiarismDetectionDescription\":\"İçeriğinizi mevcut literatürle kasıtsız benzerlikler açısından titizlikle taramak üzere tasarlanmış intihal tespit aracımızı kullanarak bütünlüğünüzü koruyun ve yazınızda özgünlüğü sürdürmenize yardımcı olun.\",\"detectPlagiarismButton\":\"Metninde İntihal Tespiti\",\"originalityScoreLabel\":\"Özgünlük Puanı\",\"plagiarismReportLabel\":\"Analiz Raporu\",\"potentialSourcesFoundLabel\":\"Potansiyel Kaynaklar Bulundu\",\"originalSourceLabel\":\"Orijinal Kaynak\",\"similarityScoreLabel\":\"Benzerlik Puanı\",\"toastPlagiarismDetectionSuccess\":\"İntihal kontrolü tamamlandı.\",\"toastPlagiarismDetectionError\":\"İntihal kontrolü başarısız oldu. Lütfen tekrar deneyin.\",\"writeSomeTextToDetectPlagiarismPlaceholder\":\"İntihal kontrolü için düzenleyiciye biraz metin yazın.\",\"aiWritingDetectionAccordionTitle\":\"Yapay Zeka Yazı Tespiti\",\"aiWritingDetectionTitle\":\"Yapay Zeka Yazı Tespiti\",\"aiWritingDetectionDescription\":\"Metninizin yapay zeka tarafından üretilmiş olma olasılığını tahmin edin.\",\"detectAiWritingButton\":\"Metninde Yazısını Tespit Et\",\"probabilityAIWrittenLabel\":\"Yapay Zeka Tarafından Yazılma Olasılığı\",\"aiWritingDetectionSummaryLabel\":\"Analiz Özeti\",\"toastAiWritingDetectionSuccess\":\"Yapay zeka yazı tespiti tamamlandı.\",\"toastAiWritingDetectionError\":\"Yapay zeka yazısı tespit edilemedi. Lütfen tekrar deneyin.\",\"writeSomeTextToDetectAiWritingPlaceholder\":\"Yapay zeka yazarlığını kontrol etmek için düzenleyiciye biraz metin yazın.\",\"writingSuggestionsTitle\":\"Yazım Önerileri\",\"analyzingTextDescription\":\"Yapay zeka metninizi öneriler için analiz ediyor...\",\"suggestionsFoundDescription\":\"{{count}} öneri bulundu. Aşağıda inceleyebilirsiniz.\",\"noSuggestionsFoundDescription\":\"Hemen bir öneri bulunamadı. Yazmaya devam edin veya yeniden ifade etmeyi deneyin.\",\"startTypingForSuggestionsDescription\":\"Yapay zeka destekli yazım önerileri için yazmaya başlayın.\",\"suggestionTypeSpelling\":\"Yazım\",\"suggestionTypeGrammar\":\"Dilbilgisi\",\"suggestionTypeRewrite\":\"Yeniden Yaz\",\"suggestionTypeStyle\":\"Stil\",\"suggestionTypeUnknown\":\"Öneri\",\"suggestionLabel\":\"Öneriyor\",\"applySuggestionButton\":\"Uygula\",\"suggestionExplanationTooltip\":\"Açıklamayı gör\",\"toastTextAnalysisError\":\"Yazım önerileri alınamadı. Lütfen tekrar deneyin.\",\"toastSuggestionAppliedSuccess\":\"Öneri uygulandı.\",\"toastSuggestionApplyError\":\"Öneri uygulanamadı. Orijinal metin değişmiş olabilir.\",\"humanizeAiTextAccordionTitle\":\"Yapay Zeka Metnini İnsanileştir\",\"humanizeAiTextTitle\":\"Yapay Zeka Metnini İnsanileştir\",\"humanizeAiTextDescription\":\"Yapay zeka tarafından üretilen metni daha insansı bir şekilde yeniden yazın.\",\"humanizeTextButton\":\"Metni İnsanileştir\",\"humanizedTextLabel\":\"İnsanileştirilmiş Metin\",\"toastHumanizeTextSuccess\":\"Metin başarıyla insanileştirildi.\",\"toastHumanizeTextError\":\"Metin insanileştirilemedi. Lütfen tekrar deneyin.\",\"writeSomeTextToHumanizePlaceholder\":\"İnsanileştirmek için düzenleyiciye yapay zeka tarafından üretilmiş metin yazın veya yapıştırın.\",\"clearEditorButton\":\"Temizle\",\"clearEditorButtonAriaLabel\":\"Düzenleyicideki tüm metni temizle\",\"toastEditorClearedSuccess\":\"Düzenleyici içeriği temizlendi.\",\"generationHistoryTitle\":\"Üretim Geçmişi\",\"noGenerationsYetPlaceholder\":\"Henüz üretim yok. Burada görmek için biraz metin üretin.\",\"promptLabel\":\"İstem\",\"outputLabel\":\"Çıktı\",\"useThisPromptButton\":\"Bu İstemi Kullan\",\"copyOutputButton\":\"Çıktıyı Kopyala\",\"toastPromptRestoredSuccess\":\"İstem giriş alanına geri yüklendi.\",\"toastTextCopiedSuccess\":\"Metin panoya kopyalandı.\",\"toastTextCopyError\":\"Metin panoya kopyalanamadı.\",\"insertIntoEditorButton\":\"Düzenleyiciye Ekle\",\"insertIntoEditorButtonTooltip\":\"Üretilen metni düzenleyiciye ekle\",\"toastTextInsertedSuccess\":\"Üretilen metin düzenleyiciye eklendi.\",\"copyEditorButton\":\"Metni Kopyala\",\"copyEditorButtonAriaLabel\":\"Düzenleyicideki tüm metni kopyala\",\"toastEditorContentCopiedSuccess\":\"Düzenleyici içeriği panoya kopyalandı.\",\"toastEditorContentCopyError\":\"Düzenleyici içeriği panoya kopyalanamadı.\",\"toastEditorEmptyForCopyError\":\"Düzenleyici boş. Kopyalanacak bir şey yok.\",\"recordVoiceButtonStart\":\"Ses Kaydet\",\"recordVoiceButtonStop\":\"Kaydı Durdur\",\"recordVoiceButtonAriaLabelStart\":\"Metne dönüştürmek için ses kaydını başlat\",\"recordVoiceButtonAriaLabelStop\":\"Ses kaydını durdur\",\"toastRecordingStarted\":\"Kayıt başladı. Mikrofonunuza konuşun.\",\"toastRecordingStoppedNoTranscript\":\"Kayıt durdu. Hiçbir konuşma yazıya dökülmedi.\",\"toastSpeechTranscribedAndAppended\":\"Konuşma yazıya döküldü ve düzenleyiciye eklendi.\",\"toastSpeechRecognitionNotSupported\":\"Tarayıcınız konuşma tanımayı desteklemiyor.\",\"toastMicrophonePermissionDenied\":\"Mikrofon izni reddedildi. Lütfen tarayıcı ayarlarınızdan etkinleştirin ve sayfayı yenileyin.\",\"toastSpeechNoSpeechDetected\":\"Hiçbir konuşma algılanmadı. Lütfen tekrar deneyin.\",\"toastSpeechAudioCaptureError\":\"Ses yakalama hatası. Lütfen mikrofonunuzu kontrol edin.\",\"toastSpeechNetworkError\":\"Konuşma tanıma sırasında ağ hatası. Lütfen bağlantınızı kontrol edin.\",\"toastSpeechRecognitionError\":\"Konuşma tanıma hatası: {{error}}\",\"toastSpeechServiceNotAllowed\":\"Konuşma tanıma hizmetine izin verilmiyor veya kullanılamıyor. Lütfen daha sonra tekrar deneyin.\",\"toastSpeechLanguageNotSupportedError\":\"Seçilen dil, tarayıcınız tarafından konuşma tanıma için desteklenmiyor.\",\"helpTitle\":\"Yardım\",\"helpPanelTitle\":\"LinguaFlow Nasıl Kullanılır\",\"helpPanelDescription\":\"LinguaFlow özellikleriyle başlayın.\",\"helpPanelIntro\":\"LinguaFlow'a hoş geldiniz! Bu kılavuz, uygulamanın güçlü yazma yardım araçlarında gezinmenize ve bunları kullanmanıza yardımcı olacaktır.\",\"helpEditorTitle\":\"Düzenleyici\",\"helpAiToolsTitle\":\"Yapay Zeka Araçları Paneli\",\"helpLanguageSettingsTitle\":\"Dil Ayarları\",\"helpAppearanceSettingsTitle\":\"Görünüm Ayarları\",\"helpDictionarySettingsTitle\":\"Sözlük Ayarları\",\"helpFeatureSettingsTitle\":\"Özellik Ayarları\",\"helpWritingAidSettingsTitle\":\"Yazım Yardımı Ayarları\",\"helpAdvancedSettingsTitle\":\"Gelişmiş Ayarlar\",\"helpEditorDescription\":\"Düzenleyici ana çalışma alanınızdır.<br/><br/><b>- Gerçek zamanlı Öneriler:</b> Siz yazarken, uygulama metninizi otomatik olarak kontrol eder ve potansiyel sorunların altını çizer. Bir düzeltme açılır penceresi görmek için vurgulanan bir bölüme tıklayın.<br/><b>- Seçimde Yapay Zeka Araçları:</b> <b>Akıllı Eş Anlamlılar</b> düğmesini getirmek için herhangi bir metin parçasını seçin. Tek bir kelime seçerseniz, eş anlamlılar ve bir telaffuz kılavuzu alırsınız. Daha uzun bir ifade seçerseniz, yapay zeka destekli bir yeniden ifade etme önerisi alırsınız.<br/><b>- Biçimlendirme Araç Çubuğu:</b> Düzenleyicinin üst kısmında, Geri Al/Yinele, yazı tiplerini değiştirme ve Kalın, İtalik, listeler ve metin hizalama gibi biçimlendirmeleri uygulama araçlarını bulacaksınız.\",\"helpAiToolsDescription\":\"Sol ve sağdaki paneller güçlü yapay zeka yetenekleri sağlar.<br/><br/><b>- Yazma Araçları (Sol):</b> Burada yapay zekanın stilini etkilemek için <b>Yazma Modu</b>'nu değiştirebilir, bir <b>Belge İçe Aktarabilir</b> veya tüm metninizi yeniden tasarlamak için <b>Yapay Zeka Yeniden Yazıcı</b>'yı kullanabilirsiniz. Ayrıca <b>Yapay Zeka Metnini İnsanileştirme</b>, <b>Yapay Zeka Yazısını</b> kontrol etme ve <b>İntihal</b> tespit etme araçlarını da kullanabilirsiniz.<br/><b>- Analiz Araçları (Sağ):</b> Bu sütun gerçek zamanlı <b>Yazma İstatistiklerini</b> gösterir, metniniz için bir <b>Ton Analizörü</b> sağlar ve bir istemden yeni metin oluşturmak için <b>Yapay Zeka İçerik Üreticisini</b> içerir. Üretim geçmişiniz kolay yeniden kullanım için burada kaydedilir.\",\"helpLanguageSettingsDescription\":\"Uygulama arayüzü için <b>Kullanıcı Arayüzü Dili</b>'ni ve analiz için <b>Birincil Yazma Dili</b>'ni yapılandırın. Bazı diller için bir <b>Bölgesel Lehçe</b> de seçebilirsiniz. Uygulamanın siz yazarken yazma dillerini değiştirmesi için <b>Otomatik Dil Algılama</b>'yı etkinleştirin.\",\"helpAppearanceSettingsDescription\":\"Uygulamanın görünümünü ve hissini özelleştirin. Bir <b>Tema</b> (Açık, Koyu veya Sistem) seçin, genel <b>Yazı Tipi Boyutu</b>'nu ayarlayın ve daha iyi okunabilirlik için <b>Yüksek Kontrast Modu</b>'nu açıp kapatın.\",\"helpDictionarySettingsDescription\":\"Farklı diller için kişisel sözlüklerinizi yönetin. Sık kullandığınız ancak yazım hatası olarak işaretlenebilecek kelimeleri (isimler veya teknik jargon gibi) <b>ekleyin</b>. Sözlük listenizi bir JSON dosyası olarak <b>içe aktarabilir</b> veya <b>dışa aktarabilir</b> veya belirli bir dil için sözlüğü <b>temizleyebilirsiniz</b>.\",\"helpFeatureSettingsDescription\":\"Belirli yapay zeka ve otomatik düzeltme özelliklerinin davranışını ince ayar yapın. Burada çeşitli üretken yapay zeka araçlarını, otomatik düzeltme davranışlarını ve iş akışınıza uyacak şekilde temel gerçek zamanlı kontrol işlevlerini açıp kapatabilirsiniz.\",\"helpWritingAidSettingsDescription\":\"Yapay zekanın size nasıl yardımcı olacağını özelleştirin. Beceri seviyenize uygun öneriler almak için <b>Dil Yeterliliğinizi</b> ayarlayın. Ayrıca <b>Ton Tespiti</b>, <b>İntihal Tespiti</b> gibi özellikleri ve <b>Anadili Olmayan Konuşmacılar</b> için özel desteği etkinleştirebilir veya devre dışı bırakabilirsiniz.\",\"helpAdvancedSettingsDescription\":\"Temel operasyonel davranışları kontrol edin. İnternet bağlantısı olmadan temel özellikleri kullanmak için <b>Çevrimdışı İşlevselliği</b> etkinleştirin. Yeni bir başlangıç yapmak isterseniz, uygulamayı orijinal varsayılanlarına geri yüklemek için <b>Tüm Ayarları Sıfırla</b>'yabilirsiniz (bu işlem geri alınamaz).\",\"helpPanelTip\":\"Yazma stilinize ve ihtiyaçlarınıza en uygun olanı bulmak için farklı araçlar ve ayarlarla denemeler yapın!\",\"Write Tools\":\"Yazma Araçları\",\"Import Document\":\"Belge İçe Aktar\",\"Quick Action\":\"Hızlı İşlem\",\"Tone Analyzer\":\"Ton Analizörü\",\"aiRewriteAccordionTitle\":\"Yapay Zeka Yeniden Yazıcı\",\"aiRewriteTitle\":\"Yapay Zeka Yeniden Yazıcı\",\"aiRewriteDescription\":\"Netliği ve stili geliştirmek için tüm düzenleyici içeriğini yeniden yazın.\",\"rewriteEditorContentButton\":\"Düzenleyici İçeriğini Yeniden Yaz\",\"rewrittenTextLabel\":\"Yeniden Yazılmış Metin\",\"applyToEditorButton\":\"Düzenleyiciye Uygula\",\"toastRewriteSuccess\":\"Düzenleyici içeriği başarıyla yeniden yazıldı.\",\"toastRewriteError\":\"Düzenleyici içeriği yeniden yazılamadı. Lütfen tekrar deneyin.\",\"writeSomeTextToRewritePlaceholder\":\"Yeniden yazmak için düzenleyiciye biraz metin yazın.\",\"Click the button to rewrite the editor content.\":\"Düzenleyici içeriğini yeniden yazmak için düğmeye tıklayın.\",\"dropzoneInstruction\":\"Dosyaları buraya bırakın veya göz atın\",\"toastFileImportSuccessTitle\":\"Dosya İçe Aktarıldı\",\"toastFileImportSuccessMessage\":\"Belge içeriği yüklendi.\",\"toastFileImportErrorTitle\":\"İçe Aktarma Hatası\",\"toastFileImportErrorMessage\":\"Dosya içeriği okunamadı. Lütfen geçerli bir .txt dosyası olduğundan emin olun.\",\"toastInvalidFileTypeMessage\":\"Geçersiz dosya türü. Yalnızca .txt dosyaları kabul edilir.\",\"dropzoneAriaLabel\":\"Belge içe aktarma bırakma alanı: Yüklemek için bir .txt dosyasına tıklayın veya sürükleyip bırakın.\",\"featuresLabel\":\"Özellikler\",\"featureSettingsDescription\":\"Belirli yazma yardım özelliklerinin işlevselliğini özelleştirin.\",\"appearanceLabel\":\"Görünüm\",\"writingAidLabel\":\"Yazım Yardımı\",\"dictionaryLabel\":\"Sözlük\",\"dictionarySettingsDescription\":\"Özel kelimeler ekleyin veya kişisel sözlükleri yönetin. (Yer tutucu)\",\"advancedSettingsLabel\":\"Gelişmiş\",\"advancedSettingsDescription\":\"Gelişmiş yapılandırma seçeneklerine erişin. Dikkatli kullanın. (Yer tutucu)\",\"uiLanguageLabel\":\"Arayüz Dili\",\"selectUiLanguagePlaceholder\":\"Arayüz dili seçin...\",\"uiLanguageDescription\":\"Uygulama arayüzünün dilini değiştirir.\",\"writingLanguageLabel\":\"Birincil Yazma Dili\",\"selectWritingLanguagePlaceholder\":\"Yazma dili seçin...\",\"writingLanguageDescription\":\"Yapay zeka analizi ve üretimi için birincil dili ayarlar.\",\"regionalDialectLabel\":\"Bölgesel Lehçe\",\"selectRegionalDialectPlaceholder\":\"Lehçe seçin...\",\"regionalDialectDescription\":\"Seçilen yazma dili için bölgesel varyasyonu belirtir.\",\"languageProficiencyLabel\":\"Dil Yeterliliği\",\"selectProficiencyPlaceholder\":\"Yeterlilik seçin...\",\"languageProficiencyDescription\":\"Yapay zekanın önerileri dil beceri seviyenize göre uyarlamasına yardımcı olur.\",\"proficiencyNative\":\"Anadil\",\"proficiencyAdvanced\":\"İleri (C1/C2)\",\"proficiencyIntermediate\":\"Orta (B1/B2)\",\"proficiencyBeginner\":\"Başlangıç (A1/A2)\",\"languageEnglishGeneral\":\"İngilizce\",\"languageSpanishGeneral\":\"İspanyolca\",\"languageFrenchGeneral\":\"Fransızca\",\"languageGermanGeneral\":\"Almanca\",\"languageItalianGeneral\":\"İtalyanca\",\"languageDutchGeneral\":\"Felemenkçe\",\"languageArabicGeneral\":\"Arapça\",\"arabicSyriaLanguage\":\"Arapça (Suriye)\",\"arabicSaudiArabiaLanguage\":\"Arapça (Suudi Arabistan)\",\"arabicEgyptLanguage\":\"Arapça (Mısır)\",\"languageTurkishGeneral\":\"Türkçe\",\"spanishSpainLanguage\":\"İspanyolca (İspanya)\",\"spanishMexicoLanguage\":\"İspanyolca (Meksika)\",\"themeLight\":\"Açık\",\"themeDark\":\"Koyu\",\"themeSystem\":\"Sistem Varsayılanı\",\"selectThemePlaceholder\":\"Tema seçin...\",\"themeDescription\":\"Uygulamanın görsel temasını seçin.\",\"fontSizeLabel\":\"Yazı Tipi Boyutu\",\"selectFontSizePlaceholder\":\"Yazı tipi boyutu seçin...\",\"fontSizeSmall\":\"Küçük\",\"fontSizeMedium\":\"Orta\",\"fontSizeLarge\":\"Büyük\",\"fontSizeDescription\":\"Uygulama genelinde metin boyutunu ayarlayın.\",\"highContrastModeLabel\":\"Yüksek Kontrast Modu\",\"highContrastModeDescription\":\"Daha iyi okunabilirlik için metin/arka plan kontrastını artırır.\",\"enabledLabel\":\"Etkin\",\"disabledLabel\":\"Devre Dışı\",\"personalDictionaryLabel\":\"Kişisel Sözlük\",\"personalDictionaryDescription\":\"Sık kullandığınız ve hata olarak işaretlenebilecek kelimeleri ekleyin.\",\"addWordPlaceholder\":\"Bir kelime girin...\",\"addWordButton\":\"Kelime Ekle\",\"deleteWordButtonAria\":\"{{word}} kelimesini sil\",\"dictionaryEmptyPlaceholder\":\"Sözlüğünüz boş. Biraz kelime ekleyin!\",\"dictionaryImportExportLabel\":\"Sözlüğü İçe / Dışa Aktar\",\"importDictionaryButton\":\"İçe Aktar\",\"exportDictionaryButton\":\"Dışa Aktar\",\"dictionaryImportExportDescription\":\"Kişisel sözlüğünüzü JSON dosyası olarak yedekleyin veya paylaşın.\",\"clearDictionaryForLanguageButton\":\"{{language}} Sözlüğünü Temizle\",\"clearDictionaryConfirmTitle\":\"Emin misiniz?\",\"clearDictionaryForLanguageConfirmDescription\":\"Bu, kişisel sözlüğünüzdeki tüm kelimeleri {{language}} dili için kalıcı olarak silecektir. Bu işlem geri alınamaz.\",\"confirmClearButton\":\"Evet, Sözlüğü Temizle\",\"clearDictionaryWarning\":\"Bu işlem geri alınamaz.\",\"toastDictionaryWordAdded\":\"'{{word}}' kelimesi sözlüğe eklendi.\",\"toastDictionaryWordExists\":\"'{{word}}' kelimesi zaten sözlükte mevcut.\",\"toastDictionaryWordEmpty\":\"Boş kelime eklenemez.\",\"toastDictionaryWordDeleted\":\"'{{word}}' kelimesi sözlükten silindi.\",\"toastDictionaryImportOverwriteSuccess\":\"{{count}} kelime içe aktarıldı, sözlük üzerine yazıldı.\",\"toastDictionaryImportMergeSuccess\":\"{{count}} yeni kelime içe aktarıldı ve birleştirildi.\",\"toastDictionaryImportInvalidFormat\":\"Geçersiz sözlük dosyası formatı. Bir JSON dizesi dizisi olmalıdır.\",\"toastDictionaryImportError\":\"Sözlük dosyası içe aktarılırken hata oluştu.\",\"toastDictionaryExportSuccess\":\"Sözlük başarıyla dışa aktarıldı.\",\"toastDictionaryCleared\":\"Kişisel sözlük temizlendi.\",\"toastLanguageSwitched\":\"Yazma dili otomatik olarak {{language}} diline değiştirildi.\",\"writingAssistanceTitle\":\"Yazma Yardımı\",\"writingAssistanceDescription\":\"LinguaFlow ile yazma deneyiminizi, size nasıl yardımcı olacağını özelleştirerek geliştirin.\",\"yourLanguageProficiencyTitle\":\"Dil Yeterliliğiniz (Birincil Dil için)\",\"yourLanguageProficiencyDescription\":\"Dil konusundaki sofistike anlayışınıza uygun, titizlikle hazırlanmış öneriler alarak yazınıza daha incelikli bir yaklaşım sağlayın.\",\"toneDetectionTitle\":\"Ton Tespiti\",\"toneDetectionDescription\":\"Metninizin duygusal alt tonlarını ton tespit özelliğimizle inceleyin; bu özellik yazınızı analiz eder ve tonunuzu iyileştirip yükseltmek için anlayışlı öneriler sunarak hedef kitlenizle daha etkili bir şekilde rezonans kurmanızı sağlar.\",\"nonNativeSupportTitle\":\"Anadili Olmayan Konuşmacılar için Destek\",\"nonNativeSupportDescription\":\"Anadili olmayan konuşmacılara yönelik özel yardımdan yararlanın; akıcılığınızı ve yazma konusundaki güveninizi artırmak için düşünceli rehberlik ve pratik ipuçları sunar.\",\"advancedSettingsTitle\":\"Gelişmiş Ayarlar\",\"enableOfflineFunctionalityLabel\":\"Çevrimdışı İşlevselliği Etkinleştir\",\"enableOfflineFunctionalityDescription\":\"Ayarlar ve sözlük dahil olmak üzere temel özellikler çevrimdışı kullanılabilir. Programın sorunsuz çalışmasını sağlamak için anahtar bileşenler dahil edilecektir.\",\"enableAutomaticLanguageDetectionLabel\":\"Otomatik Dil Algılamayı Etkinleştir\",\"enableAutomaticLanguageDetectionDescription\":\"Yazarken yazma dilini otomatik olarak algıla ve değiştir.\",\"dataManagementLabel\":\"Veri Yönetimi\",\"resetAllSettingsLabel\":\"Tüm Ayarları Sıfırla\",\"resetAllSettingsDescription\":\"Bu, tema, dil ve özellik ayarları dahil tüm özelleştirmeleri varsayılanlarına sıfırlayacaktır. Bu işlem geri alınamaz.\",\"resetButtonLabel\":\"Sıfırla\",\"resetAllSettingsConfirmTitle\":\"Tüm ayarları sıfırlamak istediğinizden emin misiniz?\",\"resetAllSettingsConfirmDescription\":\"Tüm kişisel ayarlarınız, sözlük kelimeleriniz ve tercihleriniz kalıcı olarak silinecek ve uygulama varsayılanlarına sıfırlanacaktır. Bu işlem geri alınamaz.\",\"confirmResetButton\":\"Evet, Her Şeyi Sıfırla\",\"toastResetSuccess\":\"Tüm ayarlar varsayılana sıfırlandı. Uygulama şimdi yeniden yüklenecek.\",\"dictionaryLanguageLabel\":\"Sözlük Dili\",\"selectDictionaryLanguagePlaceholder\":\"Görüntülenecek dili seçin...\",\"dictionaryLanguageDescription\":\"Belirli bir dil için sözlüğü görüntüleyin ve yönetin.\",\"toastSuggestionDismissed\":\"Öneri reddedildi.\",\"dismissButton\":\"Reddet\",\"correctButton\":\"Düzelt\",\"undoButton\":\"Geri Al\",\"redoButton\":\"İleri Al\",\"aiToolsButton\":\"Akıllı Eş Anlamlılar\",\"wordToolkitTitle\":\"Eş Anlamlı Önerisi\",\"wordToolkitDescription\":\"Eş anlamlılar almak ve telaffuzunu duymak için düzenleyicide tek bir kelime seçin.\",\"wordToolkitPlaceholder\":\"Akıllı eş anlamlılar almak için ana düzenleyicide tek bir kelime seçin.\",\"selectedWordLabel\":\"Seçili Kelime\",\"synonymsLabel\":\"Eş Anlamlılar\",\"noSynonymsFound\":\"Eş anlamlı bulunamadı.\",\"applySynonymTooltip\":\"Kelimeyi '{{synonym}}' ile değiştir\",\"toastWordToolkitError\":\"Seçili kelime için öneriler alınamadı.\",\"toastWordReplacedSuccess\":\"Kelime '{{word}}' ile değiştirildi.\",\"wordToolkitPopoverDescription\":\"Eş anlamlılar alın veya kelimenin telaffuzunu dinleyin.\",\"spellingAndPronunciationLabel\":\"Telaffuz\",\"pronounceButton\":\"Kelimeyi Telaffuz Et\",\"toastPronunciationError\":\"Sesli telaffuz üretilemedi.\",\"toastEmptyText\":\"Lütfen önce biraz metin girin.\"}"));}}),
"[project]/src/locales/es.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"appName\":\"LinguaFlow\",\"appDescription\":\"Corrección gramatical y asistente de redacción\",\"editorTitle\":\"Editor\",\"rephraseSelectionButton\":\"Reescribir Selección\",\"writingStatsTitle\":\"Estadísticas de Escritura\",\"wordCountLabel\":\"Conteo de Palabras\",\"charCountLabel\":\"Conteo de Caracteres\",\"writingScoreLabel\":\"Puntuación de Escritura\",\"writingScoreUnit\":\"/ 100\",\"writingModeLabel\":\"Modo de Escritura\",\"selectWritingModePlaceholder\":\"Seleccionar modo de escritura\",\"formalWritingMode\":\"Formal\",\"casualWritingMode\":\"Casual\",\"professionalWritingMode\":\"Profesional\",\"creativeWritingMode\":\"Creativo\",\"technicalWritingMode\":\"Técnico\",\"academicWritingMode\":\"Académico\",\"businessWritingMode\":\"Negocios\",\"aiToneAnalysisAccordionTitle\":\"Análisis de Tono\",\"aiToneAnalysisTitle\":\"Análisis de Tono\",\"aiToneAnalysisDescription\":\"Obtén retroalimentación sobre la formalidad y confianza de tu escritura.\",\"analyzeToneButton\":\"Analizar Tono del Texto\",\"formalityLabel\":\"Formalidad\",\"confidenceLabel\":\"Confianza\",\"feedbackLabel\":\"Retroalimentación\",\"writeSomeTextToAnalyzePlaceholder\":\"Escribe algo de texto en el editor para analizar su tono.\",\"aiTextGenerationAccordionTitle\":\"Generación de Contenido IA\",\"aiTextGenerationTitle\":\"Generador de Contenido IA\",\"aiTextGenerationDescription\":\"Genera contenido basado en tu indicación.\",\"yourPromptLabel\":\"Tu Indicación\",\"promptPlaceholder\":\"ej., Escribe una historia corta sobre un robot que descubre la música\",\"generatedTextLabel\":\"Texto Generado\",\"generateTextButton\":\"Generar Texto\",\"settingsAccordionTitle\":\"Configuración\",\"settingsTitle\":\"Configuración\",\"settingsDescription\":\"Personaliza tu experiencia en LinguaFlow.\",\"themeLabel\":\"Tema\",\"switchToLightMode\":\"Cambiar a Modo Claro\",\"switchToDarkMode\":\"Cambiar a Modo Oscuro\",\"languageLabel\":\"Idioma\",\"selectLanguagePlaceholder\":\"Seleccionar idioma\",\"englishUSLanguage\":\"Inglés (EE. UU.)\",\"englishUKLanguage\":\"Inglés (Reino Unido)\",\"arabicLanguage\":\"العربية (Arabic)\",\"turkishLanguage\":\"Türkçe (Turkish)\",\"spanishLanguage\":\"Español (Spanish)\",\"germanLanguage\":\"Deutsch (German)\",\"frenchLanguage\":\"Français (French)\",\"dutchLanguage\":\"Nederlands (Dutch)\",\"italianLanguage\":\"Italiano (Italian)\",\"startWritingPlaceholder\":\"Empieza a escribir aquí...\",\"rephrasePopoverTitle\":\"Reescribir Texto\",\"rephrasePopoverDescription\":\"Revisa la sugerencia para tu texto seleccionado.\",\"originalTextLabel\":\"Original\",\"suggestionTextLabel\":\"Sugerencia\",\"rephraseWaitMessage\":\"Haz clic en \\\"Reescribir\\\" o espera la sugerencia.\",\"applyButton\":\"Aplicar\",\"cancelButton\":\"Cancelar\",\"siteHeaderTitle\":\"LinguaFlow\",\"footerText\":\"Creado de Eng: AZA7© 2025. Reservados todos los derechos®. Su apoyo y reconocimiento son muy valiosos para nosotros.\",\"toastInputRequiredTitle\":\"Entrada Requerida\",\"toastEditorEmptyError\":\"El editor está vacío. Por favor, escribe algo de texto para analizar.\",\"toastPromptRequiredError\":\"Por favor, ingresa una indicación para generar texto.\",\"toastSuccessTitle\":\"Éxito\",\"toastErrorTitle\":\"Error\",\"toastInfoTitle\":\"Información\",\"toastTextGeneratedSuccess\":\"Texto generado exitosamente.\",\"toastTextGenerationError\":\"Error al generar texto. Por favor, inténtalo de nuevo.\",\"toastToneAnalysisSuccess\":\"Análisis de tono completado.\",\"toastToneAnalysisError\":\"Error al analizar el tono. Por favor, inténtalo de nuevo.\",\"toastNothingToRephraseError\":\"Nada que reescribir\",\"toastSelectTextToRephraseError\":\"Por favor, selecciona algún texto en el editor.\",\"toastSuggestionReady\":\"Sugerencia Lista\",\"toastRephraseError\":\"Error al reescribir el texto. Por favor, inténtalo de nuevo.\",\"toastFileUploadedSuccess\":\"Contenido del archivo cargado en el editor.\",\"toastFileTypeNotSupportedError\":\"Tipo de archivo no soportado. Por favor, sube un archivo {{fileType}}.\",\"plagiarismDetectionAccordionTitle\":\"Detección de Plagio IA\",\"plagiarismDetectionTitle\":\"Detección de Plagio\",\"plagiarismDetectionSettingsTitle\":\"Detección de Plagio\",\"plagiarismDetectionDescription\":\"Proteja su integridad utilizando nuestra herramienta de detección de plagio, diseñada para escanear meticulosamente su contenido en busca de similitudes no intencionadas con la literatura existente, ayudándole a mantener la originalidad en su escritura.\",\"detectPlagiarismButton\":\"Detectar Plagio en Texto\",\"originalityScoreLabel\":\"Puntuación de Originalidad\",\"plagiarismReportLabel\":\"Informe de Análisis\",\"potentialSourcesFoundLabel\":\"Posibles Fuentes Encontradas\",\"originalSourceLabel\":\"Fuente Original\",\"similarityScoreLabel\":\"Puntuación de Similitud\",\"toastPlagiarismDetectionSuccess\":\"Verificación de plagio completada.\",\"toastPlagiarismDetectionError\":\"Error al verificar el plagio. Por favor, inténtalo de nuevo.\",\"writeSomeTextToDetectPlagiarismPlaceholder\":\"Escribe algo de texto en el editor para verificar el plagio.\",\"aiWritingDetectionAccordionTitle\":\"Detección de Escritura IA\",\"aiWritingDetectionTitle\":\"Detección de Escritura IA\",\"aiWritingDetectionDescription\":\"Estima la probabilidad de que tu texto haya sido generado por IA.\",\"detectAiWritingButton\":\"Detectar Escritura IA en Texto\",\"probabilityAIWrittenLabel\":\"Probabilidad Escrito por IA\",\"aiWritingDetectionSummaryLabel\":\"Resumen del Análisis\",\"toastAiWritingDetectionSuccess\":\"Detección de escritura IA completada.\",\"toastAiWritingDetectionError\":\"Error al detectar escritura IA. Por favor, inténtalo de nuevo.\",\"writeSomeTextToDetectAiWritingPlaceholder\":\"Escribe algo de texto en el editor para verificar la autoría IA.\",\"writingSuggestionsTitle\":\"Sugerencias de Escritura\",\"analyzingTextDescription\":\"La IA está analizando tu texto en busca de sugerencias...\",\"suggestionsFoundDescription\":\"{{count}} sugerencias encontradas. Revísalas a continuación.\",\"noSuggestionsFoundDescription\":\"No se encontraron sugerencias inmediatas. Sigue escribiendo o intenta reformular.\",\"startTypingForSuggestionsDescription\":\"Comienza a escribir para obtener sugerencias de escritura impulsadas por IA.\",\"suggestionTypeSpelling\":\"Ortografía\",\"suggestionTypeGrammar\":\"Gramática\",\"suggestionTypeRewrite\":\"Reescritura\",\"suggestionTypeStyle\":\"Estilo\",\"suggestionTypeUnknown\":\"Sugerencia\",\"suggestionLabel\":\"Sugiere\",\"applySuggestionButton\":\"Aplicar\",\"suggestionExplanationTooltip\":\"Ver explicación\",\"toastTextAnalysisError\":\"Error al obtener sugerencias de escritura. Por favor, inténtalo de nuevo.\",\"toastSuggestionAppliedSuccess\":\"Sugerencia aplicada.\",\"toastSuggestionApplyError\":\"No se pudo aplicar la sugerencia. El texto original podría haber cambiado.\",\"humanizeAiTextAccordionTitle\":\"Humanizar Texto IA\",\"humanizeAiTextTitle\":\"Humanizar Texto IA\",\"humanizeAiTextDescription\":\"Reescribir texto generado por IA para que suene más humano.\",\"humanizeTextButton\":\"Humanizar Texto\",\"humanizedTextLabel\":\"Texto Humanizado\",\"toastHumanizeTextSuccess\":\"Texto humanizado exitosamente.\",\"toastHumanizeTextError\":\"Error al humanizar el texto. Por favor, inténtalo de nuevo.\",\"writeSomeTextToHumanizePlaceholder\":\"Escribe o pega texto generado por IA en el editor para humanizarlo.\",\"clearEditorButton\":\"Limpiar\",\"clearEditorButtonAriaLabel\":\"Limpiar todo el texto del editor\",\"toastEditorClearedSuccess\":\"Contenido del editor limpiado.\",\"generationHistoryTitle\":\"Historial de Generación\",\"noGenerationsYetPlaceholder\":\"Aún no hay generaciones. Genera algún texto para verlo aquí.\",\"promptLabel\":\"Indicación\",\"outputLabel\":\"Resultado\",\"useThisPromptButton\":\"Usar esta Indicación\",\"copyOutputButton\":\"Copiar Resultado\",\"toastPromptRestoredSuccess\":\"Indicación restaurada al campo de entrada.\",\"toastTextCopiedSuccess\":\"Texto copiado al portapapeles.\",\"toastTextCopyError\":\"Error al copiar texto al portapapeles.\",\"insertIntoEditorButton\":\"Insertar en Editor\",\"insertIntoEditorButtonTooltip\":\"Anexar texto generado al editor\",\"toastTextInsertedSuccess\":\"Texto generado insertado en el editor.\",\"copyEditorButton\":\"Copiar Texto\",\"copyEditorButtonAriaLabel\":\"Copiar todo el texto del editor\",\"toastEditorContentCopiedSuccess\":\"Contenido del editor copiado al portapapeles.\",\"toastEditorContentCopyError\":\"Error al copiar el contenido del editor al portapapeles.\",\"toastEditorEmptyForCopyError\":\"El editor está vacío. No hay nada que copiar.\",\"recordVoiceButtonStart\":\"Grabar Voz\",\"recordVoiceButtonStop\":\"Detener Grabación\",\"recordVoiceButtonAriaLabelStart\":\"Iniciar grabación de voz para transcribir a texto\",\"recordVoiceButtonAriaLabelStop\":\"Detener grabación de voz\",\"toastRecordingStarted\":\"Grabación iniciada. Habla a tu micrófono.\",\"toastRecordingStoppedNoTranscript\":\"Grabación detenida. No se transcribió ningún discurso.\",\"toastSpeechTranscribedAndAppended\":\"Discurso transcrito y añadido al editor.\",\"toastSpeechRecognitionNotSupported\":\"El reconocimiento de voz no es compatible con tu navegador.\",\"toastMicrophonePermissionDenied\":\"Permiso de micrófono denegado. Por favor, actívalo en la configuración de tu navegador y actualiza la página.\",\"toastSpeechNoSpeechDetected\":\"No se detectó ningún discurso. Por favor, inténtalo de nuevo.\",\"toastSpeechAudioCaptureError\":\"Error de captura de audio. Por favor, revisa tu micrófono.\",\"toastSpeechNetworkError\":\"Error de red durante el reconocimiento de voz. Por favor, revisa tu conexión.\",\"toastSpeechRecognitionError\":\"Error de reconocimiento de voz: {{error}}\",\"toastSpeechServiceNotAllowed\":\"El servicio de reconocimiento de voz no está permitido o no está disponible. Por favor, inténtalo de nuevo más tarde.\",\"toastSpeechLanguageNotSupportedError\":\"El idioma seleccionado no es compatible con el reconocimiento de voz de tu navegador.\",\"helpTitle\":\"Ayuda\",\"helpPanelTitle\":\"Cómo usar LinguaFlow\",\"helpPanelDescription\":\"Comienza con las características de LinguaFlow.\",\"helpPanelIntro\":\"¡Bienvenido a LinguaFlow! Esta guía te ayudará a navegar y usar las potentes herramientas de asistencia de escritura de la aplicación.\",\"helpEditorTitle\":\"El Editor\",\"helpAiToolsTitle\":\"Panel de Herramientas de IA\",\"helpLanguageSettingsTitle\":\"Configuración de idioma\",\"helpAppearanceSettingsTitle\":\"Configuración de apariencia\",\"helpDictionarySettingsTitle\":\"Configuración del diccionario\",\"helpFeatureSettingsTitle\":\"Configuración de características\",\"helpWritingAidSettingsTitle\":\"Configuración de ayuda para la escritura\",\"helpAdvancedSettingsTitle\":\"Configuración avanzada\",\"helpEditorDescription\":\"El Editor es tu espacio de trabajo principal.<br/><br/><b>- Sugerencias en tiempo real:</b> Mientras escribes, la aplicación revisa automáticamente tu texto y subraya posibles problemas. Haz clic en un segmento resaltado para ver un popover de corrección.<br/><b>- Herramientas de IA en la selección:</b> Selecciona cualquier fragmento de texto para que aparezca el botón <b>Sinónimos Inteligentes</b>. Si seleccionas una sola palabra, obtendrás sinónimos y una guía de pronunciación. Si seleccionas una frase más larga, obtendrás una sugerencia de reformulación impulsada por IA.<br/><b>- Barra de herramientas de formato:</b> En la parte superior del editor, encontrarás herramientas para Deshacer/Rehacer, cambiar fuentes y aplicar formato como Negrita, Cursiva, listas y alineación de texto.\",\"helpAiToolsDescription\":\"Los paneles a la izquierda y a la derecha proporcionan potentes capacidades de IA.<br/><br/><b>- Herramientas de Escritura (Izquierda):</b> Aquí puedes cambiar el <b>Modo de Escritura</b> para influir en el estilo de la IA, <b>Importar un Documento</b>, o usar el <b>Reescritor de IA</b> para redactar de nuevo todo tu texto. También puedes usar herramientas para <b>Humanizar Texto de IA</b>, verificar <b>Escritura de IA</b>, y detectar <b>Plagio</b>.<br/><b>- Herramientas de Análisis (Derecha):</b> Esta columna muestra <b>Estadísticas de Escritura</b> en tiempo real, proporciona un <b>Analizador de Tono</b> para tu texto, e incluye el <b>Generador de Contenido de IA</b> para crear nuevo texto a partir de una indicación. Tu historial de generaciones se guarda aquí para una fácil reutilización.\",\"helpLanguageSettingsDescription\":\"Configura el <b>Idioma de la Interfaz de Usuario</b> para la interfaz de la aplicación y el <b>Idioma Principal de Escritura</b> para el análisis. Para algunos idiomas, también puedes seleccionar un <b>Dialecto Regional</b>. Activa la <b>Detección Automática de Idioma</b> para que la aplicación cambie de idioma de escritura mientras escribes.\",\"helpAppearanceSettingsDescription\":\"Personaliza la apariencia de la aplicación. Elige un <b>Tema</b> (Claro, Oscuro o del Sistema), ajusta el <b>Tamaño de Fuente</b> global y activa el <b>Modo de Alto Contraste</b> para una mejor legibilidad.\",\"helpDictionarySettingsDescription\":\"Gestiona tus diccionarios personales para diferentes idiomas. <b>Añade</b> palabras que usas con frecuencia pero que podrían ser marcadas como errores de ortografía (como nombres o jerga técnica). También puedes <b>Importar</b> o <b>Exportar</b> tu lista de diccionario como un archivo JSON, o <b>Limpiar</b> el diccionario para un idioma específico.\",\"helpFeatureSettingsDescription\":\"Ajusta el comportamiento de características específicas de IA y autocorrección. Aquí puedes activar/desactivar diversas herramientas de IA generativa, comportamientos de autocorrección y las funcionalidades principales de verificación en tiempo real para que coincidan con tu flujo de trabajo.\",\"helpWritingAidSettingsDescription\":\"Personaliza cómo te ayuda la IA. Establece tu <b>Competencia Lingüística</b> para obtener sugerencias adaptadas a tu nivel de habilidad. También puedes activar o desactivar características como la <b>Detección de Tono</b>, la <b>Detección de Plagio</b>, y el soporte especializado para <b>Hablantes No Nativos</b>.\",\"helpAdvancedSettingsDescription\":\"Controla los comportamientos operativos principales. Activa la <b>Funcionalidad sin Conexión</b> para usar características básicas sin conexión a internet. Si alguna vez quieres empezar de cero, puedes <b>Restablecer Todas las Configuraciones</b> para restaurar la aplicación a sus valores predeterminados originales (esto no se puede deshacer).\",\"helpPanelTip\":\"¡Experimenta con diferentes herramientas y configuraciones para encontrar lo que mejor se adapte a tu estilo de escritura y necesidades!\",\"Write Tools\":\"Herramientas de Escritura\",\"Import Document\":\"Importar Documento\",\"Quick Action\":\"Acción Rápida\",\"Tone Analyzer\":\"Analizador de Tono\",\"aiRewriteAccordionTitle\":\"Reescritor IA\",\"aiRewriteTitle\":\"Reescritor IA\",\"aiRewriteDescription\":\"Reescribe todo el contenido del editor para mejorar la claridad y el estilo.\",\"rewriteEditorContentButton\":\"Reescribir Contenido del Editor\",\"rewrittenTextLabel\":\"Texto Reescrito\",\"applyToEditorButton\":\"Aplicar al Editor\",\"toastRewriteSuccess\":\"Contenido del editor reescrito exitosamente.\",\"toastRewriteError\":\"Error al reescribir el contenido del editor. Por favor, inténtalo de nuevo.\",\"writeSomeTextToRewritePlaceholder\":\"Escribe algo de texto en el editor para reescribirlo.\",\"Click the button to rewrite the editor content.\":\"Haz clic en el botón para reescribir el contenido del editor.\",\"dropzoneInstruction\":\"Suelta los archivos aquí o navega\",\"toastFileImportSuccessTitle\":\"Archivo Importado\",\"toastFileImportSuccessMessage\":\"Contenido del documento cargado.\",\"toastFileImportErrorTitle\":\"Error de Importación\",\"toastFileImportErrorMessage\":\"No se pudo leer el contenido del archivo. Asegúrate de que sea un archivo .txt válido.\",\"toastInvalidFileTypeMessage\":\"Tipo de archivo no válido. Solo se aceptan archivos .txt.\",\"dropzoneAriaLabel\":\"Zona de colocación para importar documentos: Haz clic o arrastra y suelta un archivo .txt para cargarlo.\",\"featuresLabel\":\"Características\",\"featureSettingsDescription\":\"Personaliza la funcionalidad de las características específicas de asistencia de escritura.\",\"appearanceLabel\":\"Apariencia\",\"writingAidLabel\":\"Ayuda para la Escritura\",\"dictionaryLabel\":\"Diccionario\",\"dictionarySettingsDescription\":\"Añadir palabras personalizadas o gestionar diccionarios personales. (Marcador de posición)\",\"advancedSettingsLabel\":\"Avanzado\",\"advancedSettingsDescription\":\"Acceder a opciones de configuración avanzadas. Usar con precaución. (Marcador de posición)\",\"uiLanguageLabel\":\"Idioma de la Interfaz\",\"selectUiLanguagePlaceholder\":\"Seleccionar idioma de la interfaz...\",\"uiLanguageDescription\":\"Cambia el idioma de la interfaz de la aplicación.\",\"writingLanguageLabel\":\"Idioma Principal de Escritura\",\"selectWritingLanguagePlaceholder\":\"Seleccionar idioma de escritura...\",\"writingLanguageDescription\":\"Establece el idioma principal para el análisis y generación de IA.\",\"regionalDialectLabel\":\"Dialecto Regional\",\"selectRegionalDialectPlaceholder\":\"Seleccionar dialecto...\",\"regionalDialectDescription\":\"Especifica la variación regional para el idioma de escritura seleccionado.\",\"languageProficiencyLabel\":\"Nivel de Competencia Lingüística\",\"selectProficiencyPlaceholder\":\"Seleccionar nivel de competencia...\",\"languageProficiencyDescription\":\"Ayuda a la IA a adaptar las sugerencias a tu nivel de habilidad lingüística.\",\"proficiencyNative\":\"Nativo\",\"proficiencyAdvanced\":\"Avanzado (C1/C2)\",\"proficiencyIntermediate\":\"Intermedio (B1/B2)\",\"proficiencyBeginner\":\"Principiante (A1/A2)\",\"languageEnglishGeneral\":\"Inglés\",\"languageSpanishGeneral\":\"Español\",\"languageFrenchGeneral\":\"Francés\",\"languageGermanGeneral\":\"Alemán\",\"languageItalianGeneral\":\"Italiano\",\"languageDutchGeneral\":\"Holandés\",\"languageArabicGeneral\":\"Árabe\",\"arabicSyriaLanguage\":\"Árabe (Siria)\",\"arabicSaudiArabiaLanguage\":\"Árabe (Arabia Saudita)\",\"arabicEgyptLanguage\":\"Árabe (Egipto)\",\"languageTurkishGeneral\":\"Turco\",\"spanishSpainLanguage\":\"Español (España)\",\"spanishMexicoLanguage\":\"Español (México)\",\"themeLight\":\"Claro\",\"themeDark\":\"Oscuro\",\"themeSystem\":\"Predeterminado del Sistema\",\"selectThemePlaceholder\":\"Seleccionar tema...\",\"themeDescription\":\"Elige el tema visual de la aplicación.\",\"fontSizeLabel\":\"Tamaño de Fuente\",\"selectFontSizePlaceholder\":\"Seleccionar tamaño de fuente...\",\"fontSizeSmall\":\"Pequeño\",\"fontSizeMedium\":\"Mediano\",\"fontSizeLarge\":\"Grande\",\"fontSizeDescription\":\"Ajusta el tamaño del texto en toda la aplicación.\",\"highContrastModeLabel\":\"Modo de Alto Contraste\",\"highContrastModeDescription\":\"Aumenta el contraste texto/fondo para una mejor legibilidad.\",\"enabledLabel\":\"Activado\",\"disabledLabel\":\"Desactivado\",\"personalDictionaryLabel\":\"Diccionario Personal\",\"personalDictionaryDescription\":\"Añade palabras que usas frecuentemente y que podrían ser marcadas como errores.\",\"addWordPlaceholder\":\"Ingresar una palabra...\",\"addWordButton\":\"Añadir Palabra\",\"deleteWordButtonAria\":\"Eliminar palabra {{word}}\",\"dictionaryEmptyPlaceholder\":\"Tu diccionario está vacío. ¡Añade algunas palabras!\",\"dictionaryImportExportLabel\":\"Importar / Exportar Diccionario\",\"importDictionaryButton\":\"Importar\",\"exportDictionaryButton\":\"Exportar\",\"dictionaryImportExportDescription\":\"Haz una copia de seguridad o comparte tu diccionario personal como un archivo JSON.\",\"clearDictionaryForLanguageButton\":\"Limpiar Diccionario de {{language}}\",\"clearDictionaryConfirmTitle\":\"¿Estás seguro?\",\"clearDictionaryForLanguageConfirmDescription\":\"Esto eliminará permanentemente todas las palabras de tu diccionario personal para {{language}}. Esta acción no se puede deshacer.\",\"confirmClearButton\":\"Sí, Limpiar Diccionario\",\"clearDictionaryWarning\":\"Esta acción es irreversible.\",\"toastDictionaryWordAdded\":\"Palabra '{{word}}' añadida al diccionario.\",\"toastDictionaryWordExists\":\"La palabra '{{word}}' ya existe en el diccionario.\",\"toastDictionaryWordEmpty\":\"No se puede añadir una palabra vacía.\",\"toastDictionaryWordDeleted\":\"Palabra '{{word}}' eliminada del diccionario.\",\"toastDictionaryImportOverwriteSuccess\":\"{{count}} palabras importadas, diccionario sobrescrito.\",\"toastDictionaryImportMergeSuccess\":\"{{count}} nuevas palabras importadas y fusionadas.\",\"toastDictionaryImportInvalidFormat\":\"Formato de archivo de diccionario no válido. Debe ser un array JSON de cadenas.\",\"toastDictionaryImportError\":\"Error al importar el archivo del diccionario.\",\"toastDictionaryExportSuccess\":\"Diccionario exportado exitosamente.\",\"toastDictionaryCleared\":\"Diccionario personal limpiado.\",\"toastLanguageSwitched\":\"Idioma de escritura cambiado automáticamente a {{language}}.\",\"writingAssistanceTitle\":\"Asistencia de Escritura\",\"writingAssistanceDescription\":\"Mejore su experiencia de escritura con LinguaFlow personalizando cómo le asiste.\",\"yourLanguageProficiencyTitle\":\"Su Competencia Lingüística (para el Idioma Principal)\",\"yourLanguageProficiencyDescription\":\"Reciba sugerencias meticulosamente adaptadas que se alinean con su sofisticada comprensión del lenguaje, asegurando un enfoque más matizado en su escritura.\",\"toneDetectionTitle\":\"Detección de Tono\",\"toneDetectionDescription\":\"Profundice en los matices emocionales de su texto con nuestra función de detección de tono, que analiza su escritura y ofrece sugerencias perspicaces para refinar y elevar su tono, haciéndolo resonar más eficazmente con su audiencia.\",\"nonNativeSupportTitle\":\"Soporte para Hablantes No Nativos\",\"nonNativeSupportDescription\":\"Benefíciese de asistencia especializada dirigida a hablantes no nativos, que proporciona orientación reflexiva y consejos prácticos para mejorar su fluidez y confianza al escribir.\",\"advancedSettingsTitle\":\"Configuración Avanzada\",\"enableOfflineFunctionalityLabel\":\"Habilitar Funcionalidad sin Conexión\",\"enableOfflineFunctionalityDescription\":\"Las características básicas, incluyendo configuraciones y el diccionario, están disponibles sin conexión. Se incluirán componentes clave para asegurar un funcionamiento fluido del programa.\",\"enableAutomaticLanguageDetectionLabel\":\"Habilitar Detección Automática de Idioma\",\"enableAutomaticLanguageDetectionDescription\":\"Detecta y cambia automáticamente el idioma de escritura mientras escribes.\",\"dataManagementLabel\":\"Gestión de Datos\",\"resetAllSettingsLabel\":\"Restablecer Todas las Configuraciones\",\"resetAllSettingsDescription\":\"Esto restablecerá todas las personalizaciones, incluyendo el tema, el idioma y la configuración de las funciones, a sus valores predeterminados. Esta acción no se puede deshacer.\",\"resetButtonLabel\":\"Restablecer\",\"resetAllSettingsConfirmTitle\":\"¿Estás seguro de que quieres restablecer todas las configuraciones?\",\"resetAllSettingsConfirmDescription\":\"Todas tus configuraciones personales, palabras del diccionario y preferencias se eliminarán permanentemente y se restablecerán a los valores predeterminados de la aplicación. Esta acción no se puede deshacer.\",\"confirmResetButton\":\"Sí, Restablecer Todo\",\"toastResetSuccess\":\"Todas las configuraciones se han restablecido a los valores predeterminados. La aplicación se recargará ahora.\",\"dictionaryLanguageLabel\":\"Idioma del Diccionario\",\"selectDictionaryLanguagePlaceholder\":\"Seleccionar idioma para ver...\",\"dictionaryLanguageDescription\":\"Ver y gestionar el diccionario para un idioma específico.\",\"toastSuggestionDismissed\":\"Sugerencia descartada.\",\"dismissButton\":\"Descartar\",\"correctButton\":\"Corregir\",\"undoButton\":\"Deshacer\",\"redoButton\":\"Rehacer\",\"aiToolsButton\":\"Sinónimos Inteligentes\",\"wordToolkitTitle\":\"Sugerencia de Sinónimos\",\"wordToolkitDescription\":\"Seleccione una sola palabra en el editor para obtener sinónimos y escuchar su pronunciación.\",\"wordToolkitPlaceholder\":\"Seleccione una sola palabra en el editor principal para obtener sinónimos inteligentes.\",\"selectedWordLabel\":\"Palabra Seleccionada\",\"synonymsLabel\":\"Sinónimos\",\"noSynonymsFound\":\"No se encontraron sinónimos.\",\"applySynonymTooltip\":\"Reemplazar palabra con '{{synonym}}'\",\"toastWordToolkitError\":\"Error al obtener sugerencias para la palabra seleccionada.\",\"toastWordReplacedSuccess\":\"Palabra reemplazada por '{{word}}'.\",\"wordToolkitPopoverDescription\":\"Obtén sinónimos o escucha la pronunciación de la palabra.\",\"spellingAndPronunciationLabel\":\"Pronunciación\",\"pronounceButton\":\"Pronunciar Palabra\",\"toastPronunciationError\":\"Error al generar la pronunciación de audio.\",\"toastEmptyText\":\"Por favor, ingrese algo de texto primero.\"}"));}}),
"[project]/src/locales/de.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"appName\":\"LinguaFlow\",\"appDescription\":\"Grammatikkorrektur & Schreibassistent\",\"editorTitle\":\"Editor\",\"rephraseSelectionButton\":\"Auswahl umformulieren\",\"writingStatsTitle\":\"Schreibstatistiken\",\"wordCountLabel\":\"Wortanzahl\",\"charCountLabel\":\"Zeichenanzahl\",\"writingScoreLabel\":\"Schreib-Score\",\"writingScoreUnit\":\"/ 100\",\"writingModeLabel\":\"Schreibmodus\",\"selectWritingModePlaceholder\":\"Schreibmodus auswählen\",\"formalWritingMode\":\"Formell\",\"casualWritingMode\":\"Zwanglos\",\"professionalWritingMode\":\"Professionell\",\"creativeWritingMode\":\"Kreativ\",\"technicalWritingMode\":\"Technisch\",\"academicWritingMode\":\"Akademisch\",\"businessWritingMode\":\"Geschäftlich\",\"aiToneAnalysisAccordionTitle\":\"KI-Tonanalyse\",\"aiToneAnalysisTitle\":\"KI-Tonanalyse\",\"aiToneAnalysisDescription\":\"Erhalten Sie Feedback zur Formalität und Souveränität Ihres Schreibens.\",\"analyzeToneButton\":\"Ton Textes analysieren\",\"formalityLabel\":\"Formalität\",\"confidenceLabel\":\"Souveränität\",\"feedbackLabel\":\"Feedback\",\"writeSomeTextToAnalyzePlaceholder\":\"Schreiben Sie Text in den Editor, um dessen Ton zu analysieren.\",\"aiTextGenerationAccordionTitle\":\"KI-Inhaltsgenerierung\",\"aiTextGenerationTitle\":\"KI-Inhaltsgenerator\",\"aiTextGenerationDescription\":\"Generieren Sie Inhalte basierend auf Ihrer Eingabeaufforderung.\",\"yourPromptLabel\":\"Ihre Eingabeaufforderung\",\"promptPlaceholder\":\"z.B. Schreiben Sie eine Kurzgeschichte über einen Roboter, der Musik entdeckt\",\"generatedTextLabel\":\"Generierter Text\",\"generateTextButton\":\"Text generieren\",\"settingsAccordionTitle\":\"Einstellungen\",\"settingsTitle\":\"Einstellungen\",\"settingsDescription\":\"Passen Sie Ihr LinguaFlow-Erlebnis an.\",\"themeLabel\":\"Thema\",\"switchToLightMode\":\"Zum Hellen Modus wechseln\",\"switchToDarkMode\":\"Zum Dunklen Modus wechseln\",\"languageLabel\":\"Sprache\",\"selectLanguagePlaceholder\":\"Sprache auswählen\",\"englishUSLanguage\":\"Englisch (US)\",\"englishUKLanguage\":\"Englisch (UK)\",\"arabicLanguage\":\"العربية (Arabic)\",\"turkishLanguage\":\"Türkçe (Turkish)\",\"spanishLanguage\":\"Español (Spanish)\",\"germanLanguage\":\"Deutsch (German)\",\"frenchLanguage\":\"Français (French)\",\"dutchLanguage\":\"Nederlands (Dutch)\",\"italianLanguage\":\"Italiano (Italian)\",\"startWritingPlaceholder\":\"Beginnen Sie hier zu schreiben...\",\"rephrasePopoverTitle\":\"Text umformulieren\",\"rephrasePopoverDescription\":\"Überprüfen Sie den Vorschlag für Ihren ausgewählten Text.\",\"originalTextLabel\":\"Original\",\"suggestionTextLabel\":\"Vorschlag\",\"rephraseWaitMessage\":\"Klicken Sie auf \\\"Umformulieren\\\" oder warten Sie auf den Vorschlag.\",\"applyButton\":\"Anwenden\",\"cancelButton\":\"Abbrechen\",\"siteHeaderTitle\":\"LinguaFlow\",\"footerText\":\"Erstellt von Eng: AZA7© 2025. Alle Rechte vorbehalten®. Wir freuen uns sehr über Ihre Unterstützung und Anerkennung.\",\"toastInputRequiredTitle\":\"Eingabe erforderlich\",\"toastEditorEmptyError\":\"Der Editor ist leer. Bitte schreiben Sie Text, um ihn zu analysieren.\",\"toastPromptRequiredError\":\"Bitte geben Sie eine Eingabeaufforderung ein, um Text zu generieren.\",\"toastSuccessTitle\":\"Erfolg\",\"toastErrorTitle\":\"Fehler\",\"toastInfoTitle\":\"Info\",\"toastTextGeneratedSuccess\":\"Text erfolgreich generiert.\",\"toastTextGenerationError\":\"Fehler beim Generieren des Textes. Bitte versuchen Sie es erneut.\",\"toastToneAnalysisSuccess\":\"Tonanalyse abgeschlossen.\",\"toastToneAnalysisError\":\"Fehler bei der Tonanalyse. Bitte versuchen Sie es erneut.\",\"toastNothingToRephraseError\":\"Nichts zum Umformulieren\",\"toastSelectTextToRephraseError\":\"Bitte wählen Sie Text im Editor aus.\",\"toastSuggestionReady\":\"Vorschlag bereit\",\"toastRephraseError\":\"Fehler beim Umformulieren des Textes. Bitte versuchen Sie es erneut.\",\"toastFileUploadedSuccess\":\"Dateiinhalt in Editor geladen.\",\"toastFileTypeNotSupportedError\":\"Dateityp nicht unterstützt. Bitte laden Sie eine {{fileType}}-Datei hoch.\",\"plagiarismDetectionAccordionTitle\":\"Plagiatserkennung\",\"plagiarismDetectionTitle\":\"Plagiatserkennung\",\"plagiarismDetectionSettingsTitle\":\"Plagiatserkennung\",\"plagiarismDetectionDescription\":\"Schützen Sie Ihre Integrität, indem Sie unser Plagiatserkennungstool verwenden, das Ihren Inhalt sorgfältig auf unbeabsichtigte Ähnlichkeiten mit bestehender Literatur überprüft und Ihnen hilft, die Originalität Ihres Schreibens zu wahren.\",\"detectPlagiarismButton\":\"Plagiatserkennung\",\"originalityScoreLabel\":\"Originalitäts-Score\",\"plagiarismReportLabel\":\"Analysebericht\",\"potentialSourcesFoundLabel\":\"Mögliche Quellen gefunden\",\"originalSourceLabel\":\"Originalquelle\",\"similarityScoreLabel\":\"Ähnlichkeits-Score\",\"toastPlagiarismDetectionSuccess\":\"Plagiatsprüfung abgeschlossen.\",\"toastPlagiarismDetectionError\":\"Fehler bei der Plagiatsprüfung. Bitte versuchen Sie es erneut.\",\"writeSomeTextToDetectPlagiarismPlaceholder\":\"Schreiben Sie Text in den Editor, um auf Plagiate zu prüfen.\",\"aiWritingDetectionAccordionTitle\":\"KI-Schrifterkennung\",\"aiWritingDetectionTitle\":\"KI-Schrifterkennung\",\"aiWritingDetectionDescription\":\"Schätzen Sie die Wahrscheinlichkeit, dass Ihr Text KI-generiert wurde.\",\"detectAiWritingButton\":\"KI-Texte erkennen\",\"probabilityAIWrittenLabel\":\"Wahrscheinlichkeit KI-geschrieben\",\"aiWritingDetectionSummaryLabel\":\"Analyse-Zusammenfassung\",\"toastAiWritingDetectionSuccess\":\"KI-Schrifterkennung abgeschlossen.\",\"toastAiWritingDetectionError\":\"Fehler bei der Erkennung von KI-Schrift. Bitte versuchen Sie es erneut.\",\"writeSomeTextToDetectAiWritingPlaceholder\":\"Schreiben Sie Text in den Editor, um auf KI-Autorschaft zu prüfen.\",\"writingSuggestionsTitle\":\"Schreibvorschläge\",\"analyzingTextDescription\":\"KI analysiert Ihren Text nach Vorschlägen...\",\"suggestionsFoundDescription\":\"{{count}} Vorschläge gefunden. Überprüfen Sie sie unten.\",\"noSuggestionsFoundDescription\":\"Keine sofortigen Vorschläge gefunden. Schreiben Sie weiter oder versuchen Sie es mit einer Umformulierung.\",\"startTypingForSuggestionsDescription\":\"Beginnen Sie mit dem Tippen für KI-gestützte Schreibvorschläge.\",\"suggestionTypeSpelling\":\"Rechtschreibung\",\"suggestionTypeGrammar\":\"Grammatik\",\"suggestionTypeRewrite\":\"Umformulierung\",\"suggestionTypeStyle\":\"Stil\",\"suggestionTypeUnknown\":\"Vorschlag\",\"suggestionLabel\":\"Schlägt vor\",\"applySuggestionButton\":\"Anwenden\",\"suggestionExplanationTooltip\":\"Erläuterung anzeigen\",\"toastTextAnalysisError\":\"Schreibvorschläge konnten nicht abgerufen werden. Bitte versuchen Sie es erneut.\",\"toastSuggestionAppliedSuccess\":\"Vorschlag angewendet.\",\"toastSuggestionApplyError\":\"Vorschlag konnte nicht angewendet werden. Der Originaltext hat sich möglicherweise geändert.\",\"humanizeAiTextAccordionTitle\":\"KI-Text vermenschlichen\",\"humanizeAiTextTitle\":\"KI-Text vermenschlichen\",\"humanizeAiTextDescription\":\"KI-generierten Text umschreiben, damit er menschlicher klingt.\",\"humanizeTextButton\":\"Texte humanisieren\",\"humanizedTextLabel\":\"Vermenschlichter Text\",\"toastHumanizeTextSuccess\":\"Text erfolgreich vermenschlicht.\",\"toastHumanizeTextError\":\"Fehler beim Vermenschlichen des Textes. Bitte versuchen Sie es erneut.\",\"writeSomeTextToHumanizePlaceholder\":\"Schreiben oder fügen Sie KI-generierten Text in den Editor ein, um ihn zu vermenschlichen.\",\"clearEditorButton\":\"Löschen\",\"clearEditorButtonAriaLabel\":\"Gesamten Text aus dem Editor löschen\",\"toastEditorClearedSuccess\":\"Editor-Inhalt gelöscht.\",\"generationHistoryTitle\":\"Generierungsverlauf\",\"noGenerationsYetPlaceholder\":\"Noch keine Generierungen. Generieren Sie Text, um ihn hier zu sehen.\",\"promptLabel\":\"Eingabeaufforderung\",\"outputLabel\":\"Ausgabe\",\"useThisPromptButton\":\"Diese Eingabeaufforderung verwenden\",\"copyOutputButton\":\"Ausgabe kopieren\",\"toastPromptRestoredSuccess\":\"Eingabeaufforderung im Eingabefeld wiederhergestellt.\",\"toastTextCopiedSuccess\":\"Text in die Zwischenablage kopiert.\",\"toastTextCopyError\":\"Fehler beim Kopieren des Textes in die Zwischenablage.\",\"insertIntoEditorButton\":\"In Editor einfügen\",\"insertIntoEditorButtonTooltip\":\"Generierten Text an den Editor anhängen\",\"toastTextInsertedSuccess\":\"Generierter Text in Editor eingefügt.\",\"copyEditorButton\":\"Text kopieren\",\"copyEditorButtonAriaLabel\":\"Gesamten Text aus dem Editor kopieren\",\"toastEditorContentCopiedSuccess\":\"Editor-Inhalt in die Zwischenablage kopiert.\",\"toastEditorContentCopyError\":\"Fehler beim Kopieren des Editor-Inhalts in die Zwischenablage.\",\"toastEditorEmptyForCopyError\":\"Der Editor ist leer. Nichts zu kopieren.\",\"recordVoiceButtonStart\":\"Sprachaufnahme\",\"recordVoiceButtonStop\":\"Aufnahme stoppen\",\"recordVoiceButtonAriaLabelStart\":\"Sprachaufnahme starten, um sie in Text umzuwandeln\",\"recordVoiceButtonAriaLabelStop\":\"Sprachaufnahme stoppen\",\"toastRecordingStarted\":\"Aufnahme gestartet. Sprechen Sie in Ihr Mikrofon.\",\"toastRecordingStoppedNoTranscript\":\"Aufnahme gestoppt. Es wurde keine Sprache transkribiert.\",\"toastSpeechTranscribedAndAppended\":\"Sprache transkribiert und dem Editor hinzugefügt.\",\"toastSpeechRecognitionNotSupported\":\"Die Spracherkennung wird von Ihrem Browser nicht unterstützt.\",\"toastMicrophonePermissionDenied\":\"Mikrofonberechtigung verweigert. Bitte aktivieren Sie sie in Ihren Browsereinstellungen und aktualisieren Sie die Seite.\",\"toastSpeechNoSpeechDetected\":\"Es wurde keine Sprache erkannt. Bitte versuchen Sie es erneut.\",\"toastSpeechAudioCaptureError\":\"Fehler bei der Audioaufnahme. Bitte überprüfen Sie Ihr Mikrofon.\",\"toastSpeechNetworkError\":\"Netzwerkfehler während der Spracherkennung. Bitte überprüfen Sie Ihre Verbindung.\",\"toastSpeechRecognitionError\":\"Fehler bei der Spracherkennung: {{error}}\",\"toastSpeechServiceNotAllowed\":\"Der Spracherkennungsdienst ist nicht zulässig oder nicht verfügbar. Bitte versuchen Sie es später erneut.\",\"toastSpeechLanguageNotSupportedError\":\"Die ausgewählte Sprache wird von Ihrem Browser nicht für die Spracherkennung unterstützt.\",\"helpTitle\":\"Hilfe\",\"helpPanelTitle\":\"So verwenden Sie LinguaFlow\",\"helpPanelDescription\":\"Erste Schritte mit den Funktionen von LinguaFlow.\",\"helpPanelIntro\":\"Willkommen bei LinguaFlow! Diese Anleitung hilft Ihnen bei der Navigation und Nutzung der leistungsstarken Schreibhilfetools der Anwendung.\",\"helpEditorTitle\":\"Der Editor\",\"helpAiToolsTitle\":\"KI-Tools-Panel\",\"helpLanguageSettingsTitle\":\"Spracheinstellungen\",\"helpAppearanceSettingsTitle\":\"Erscheinungsbildeinstellungen\",\"helpDictionarySettingsTitle\":\"Wörterbucheinstellungen\",\"helpFeatureSettingsTitle\":\"Funktionseinstellungen\",\"helpWritingAidSettingsTitle\":\"Schreibhilfe-Einstellungen\",\"helpAdvancedSettingsTitle\":\"Erweiterte Einstellungen\",\"helpEditorDescription\":\"Der Editor ist Ihr Hauptarbeitsbereich.<br/><br/><b>- Echtzeit-Vorschläge:</b> Während Sie tippen, überprüft die App automatisch Ihren Text und unterstreicht potenzielle Probleme. Klicken Sie auf ein hervorgehobenes Segment, um ein Korrektur-Popover anzuzeigen.<br/><b>- KI-Tools bei Auswahl:</b> Wählen Sie einen beliebigen Textabschnitt aus, um die Schaltfläche <b>Intelligente Synonyme</b> anzuzeigen. Wenn Sie ein einzelnes Wort auswählen, erhalten Sie Synonyme und eine Aussprachehilfe. Wenn Sie einen längeren Satz auswählen, erhalten Sie einen KI-gestützten Umformulierungsvorschlag.<br/><b>- Formatierungssymbolleiste:</b> Oben im Editor finden Sie Werkzeuge zum Rückgängigmachen/Wiederherstellen, zum Ändern von Schriftarten und zum Anwenden von Formatierungen wie Fett, Kursiv, Listen und Textausrichtung.\",\"helpAiToolsDescription\":\"Die Panels auf der linken und rechten Seite bieten leistungsstarke KI-Funktionen.<br/><br/><b>- Schreibwerkzeuge (Links):</b> Hier können Sie den <b>Schreibmodus</b> ändern, um den Stil der KI zu beeinflussen, ein <b>Dokument importieren</b> oder den <b>KI-Umschreiber</b> verwenden, um Ihren gesamten Text neu zu entwerfen. Sie können auch Werkzeuge verwenden, um <b>KI-Text zu vermenschlichen</b>, auf <b>KI-Schreiben</b> zu prüfen und <b>Plagiate</b> zu erkennen.<br/><b>- Analysewerkzeuge (Rechts):</b> Diese Spalte zeigt Echtzeit-<b>Schreibstatistiken</b>, bietet einen <b>Tonanalysator</b> für Ihren Text und enthält den <b>KI-Inhaltsgenerator</b>, um neuen Text aus einer Eingabeaufforderung zu erstellen. Ihr Generierungsverlauf wird hier zur einfachen Wiederverwendung gespeichert.\",\"helpLanguageSettingsDescription\":\"Konfigurieren Sie die <b>UI-Sprache</b> für die Anwendungsoberfläche und die <b>Primäre Schreibsprache</b> für die Analyse. Für einige Sprachen können Sie auch einen <b>Regionalen Dialekt</b> auswählen. Aktivieren Sie die <b>Automatische Spracherkennung</b>, damit die App die Schreibsprachen während der Eingabe wechselt.\",\"helpAppearanceSettingsDescription\":\"Passen Sie das Erscheinungsbild der App an. Wählen Sie ein <b>Thema</b> (Hell, Dunkel oder System), passen Sie die globale <b>Schriftgröße</b> an und schalten Sie den <b>Modus für hohen Kontrast</b> für eine bessere Lesbarkeit um.\",\"helpDictionarySettingsDescription\":\"Verwalten Sie Ihre persönlichen Wörterbücher für verschiedene Sprachen. <b>Fügen Sie</b> Wörter hinzu, die Sie häufig verwenden, aber möglicherweise als Rechtschreibfehler markiert werden (wie Namen oder Fachjargon). Sie können Ihre Wörterbuchliste auch als JSON-Datei <b>importieren</b> oder <b>exportieren</b> oder das Wörterbuch für eine bestimmte Sprache <b>löschen</b>.\",\"helpFeatureSettingsDescription\":\"Passen Sie das Verhalten bestimmter KI- und Autokorrekturfunktionen an. Hier können Sie verschiedene generative KI-Tools, Autokorrekturverhalten und die wichtigsten Echtzeit-Überprüfungsfunktionen ein- und ausschalten, um sie an Ihren Arbeitsablauf anzupassen.\",\"helpWritingAidSettingsDescription\":\"Passen Sie an, wie die KI Sie unterstützt. Stellen Sie Ihre <b>Sprachkompetenz</b> ein, um auf Ihr Können zugeschnittene Vorschläge zu erhalten. Sie können auch Funktionen wie <b>Tonerkennung</b>, <b>Plagiatserkennung</b> und spezielle Unterstützung für <b>Nicht-Muttersprachler</b> aktivieren oder deaktivieren.\",\"helpAdvancedSettingsDescription\":\"Steuern Sie wichtige Betriebsverhalten. Aktivieren Sie die <b>Offline-Funktionalität</b>, um grundlegende Funktionen ohne Internetverbindung zu nutzen. Wenn Sie jemals von vorne anfangen möchten, können Sie <b>Alle Einstellungen zurücksetzen</b>, um die Anwendung auf ihre ursprünglichen Standardeinstellungen zurückzusetzen (dies kann nicht rückgängig gemacht werden).\",\"helpPanelTip\":\"Experimentieren Sie mit verschiedenen Tools und Einstellungen, um herauszufinden, was für Ihren Schreibstil und Ihre Bedürfnisse am besten funktioniert!\",\"Write Tools\":\"Schreibwerkzeuge\",\"Import Document\":\"Dokument importieren\",\"Quick Action\":\"Schnellaktion\",\"Tone Analyzer\":\"Tonanalysator\",\"aiRewriteAccordionTitle\":\"KI-Umschreiber\",\"aiRewriteTitle\":\"KI-Umschreiber\",\"aiRewriteDescription\":\"Den gesamten Editorinhalt umschreiben, um Klarheit und Stil zu verbessern.\",\"rewriteEditorContentButton\":\"Editorinhalt umschreiben\",\"rewrittenTextLabel\":\"Umgeschriebener Text\",\"applyToEditorButton\":\"Auf Editor anwenden\",\"toastRewriteSuccess\":\"Editorinhalt erfolgreich umgeschrieben.\",\"toastRewriteError\":\"Fehler beim Umschreiben des Editorinhalts. Bitte versuchen Sie es erneut.\",\"writeSomeTextToRewritePlaceholder\":\"Schreiben Sie Text in den Editor, um ihn umzuschreiben.\",\"Click the button to rewrite the editor content.\":\"Klicken Sie auf die Schaltfläche, um den Editorinhalt umzuschreiben.\",\"dropzoneInstruction\":\"Dateien hier ablegen oder durchsuchen\",\"toastFileImportSuccessTitle\":\"Datei importiert\",\"toastFileImportSuccessMessage\":\"Dokumentinhalt wurde geladen.\",\"toastFileImportErrorTitle\":\"Importfehler\",\"toastFileImportErrorMessage\":\"Der Dateiinhalt konnte nicht gelesen werden. Bitte stellen Sie sicher, dass es sich um eine gültige .txt-Datei handelt.\",\"toastInvalidFileTypeMessage\":\"Ungültiger Dateityp. Es werden nur .txt-Dateien akzeptiert.\",\"dropzoneAriaLabel\":\"Import-Dropzone für Dokumente: Klicken oder ziehen Sie eine .txt-Datei hierher, um sie hochzuladen.\",\"featuresLabel\":\"Funktionen\",\"featureSettingsDescription\":\"Passt die Funktionalität bestimmter Schreibassistenzfunktionen an.\",\"appearanceLabel\":\"Erscheinungsbild\",\"writingAidLabel\":\"Schreibhilfe\",\"dictionaryLabel\":\"Wörterbuch\",\"dictionarySettingsDescription\":\"Benutzerdefinierte Wörter hinzufügen oder persönliche Wörterbücher verwalten. (Platzhalter)\",\"advancedSettingsLabel\":\"Erweitert\",\"advancedSettingsDescription\":\"Zugriff auf erweiterte Konfigurationsoptionen. Mit Vorsicht verwenden. (Platzhalter)\",\"uiLanguageLabel\":\"Benutzeroberflächensprache\",\"selectUiLanguagePlaceholder\":\"Benutzeroberflächensprache auswählen...\",\"uiLanguageDescription\":\"Ändert die Sprache der Anwendungsoberfläche.\",\"writingLanguageLabel\":\"Primäre Schreibsprache\",\"selectWritingLanguagePlaceholder\":\"Schreibsprache auswählen...\",\"writingLanguageDescription\":\"Legt die primäre Sprache für KI-Analyse und -Generierung fest.\",\"regionalDialectLabel\":\"Regionaler Dialekt\",\"selectRegionalDialectPlaceholder\":\"Dialekt auswählen...\",\"regionalDialectDescription\":\"Gibt die regionale Variante für die ausgewählte Schreibsprache an.\",\"languageProficiencyLabel\":\"Sprachkompetenz\",\"selectProficiencyPlaceholder\":\"Kompetenz auswählen...\",\"languageProficiencyDescription\":\"Hilft der KI, Vorschläge an Ihr Sprachniveau anzupassen.\",\"proficiencyNative\":\"Muttersprachler\",\"proficiencyAdvanced\":\"Fortgeschritten (C1/C2)\",\"proficiencyIntermediate\":\"Mittelstufe (B1/B2)\",\"proficiencyBeginner\":\"Anfänger (A1/A2)\",\"languageEnglishGeneral\":\"Englisch\",\"languageSpanishGeneral\":\"Spanisch\",\"languageFrenchGeneral\":\"Französisch\",\"languageGermanGeneral\":\"Deutsch\",\"languageItalianGeneral\":\"Italienisch\",\"languageDutchGeneral\":\"Niederländisch\",\"languageArabicGeneral\":\"Arabisch\",\"arabicSyriaLanguage\":\"Arabisch (Syrien)\",\"arabicSaudiArabiaLanguage\":\"Arabisch (Saudi-Arabien)\",\"arabicEgyptLanguage\":\"Arabisch (Ägypten)\",\"languageTurkishGeneral\":\"Türkisch\",\"spanishSpainLanguage\":\"Spanisch (Spanien)\",\"spanishMexicoLanguage\":\"Spanisch (Mexiko)\",\"themeLight\":\"Hell\",\"themeDark\":\"Dunkel\",\"themeSystem\":\"Systemstandard\",\"selectThemePlaceholder\":\"Thema auswählen...\",\"themeDescription\":\"Wählen Sie das visuelle Thema der Anwendung.\",\"fontSizeLabel\":\"Schriftgröße\",\"selectFontSizePlaceholder\":\"Schriftgröße auswählen...\",\"fontSizeSmall\":\"Klein\",\"fontSizeMedium\":\"Mittel\",\"fontSizeLarge\":\"Groß\",\"fontSizeDescription\":\"Passt die Textgröße in der gesamten Anwendung an.\",\"highContrastModeLabel\":\"Modus für hohen Kontrast\",\"highContrastModeDescription\":\"Erhöht den Text-/Hintergrundkontrast für bessere Lesbarkeit.\",\"enabledLabel\":\"Aktiviert\",\"disabledLabel\":\"Deaktiviert\",\"personalDictionaryLabel\":\"Persönliches Wörterbuch\",\"personalDictionaryDescription\":\"Fügen Sie häufig verwendete Wörter hinzu, die möglicherweise als Fehler markiert werden.\",\"addWordPlaceholder\":\"Wort eingeben...\",\"addWordButton\":\"Wort hinzufügen\",\"deleteWordButtonAria\":\"Wort {{word}} löschen\",\"dictionaryEmptyPlaceholder\":\"Ihr Wörterbuch ist leer. Fügen Sie einige Wörter hinzu!\",\"dictionaryImportExportLabel\":\"Wörterbuch importieren / exportieren\",\"importDictionaryButton\":\"Importieren\",\"exportDictionaryButton\":\"Exportieren\",\"dictionaryImportExportDescription\":\"Sichern oder teilen Sie Ihr persönliches Wörterbuch als JSON-Datei.\",\"clearDictionaryForLanguageButton\":\"{{language}} Wörterbuch löschen\",\"clearDictionaryConfirmTitle\":\"Sind Sie sicher?\",\"clearDictionaryForLanguageConfirmDescription\":\"Dadurch werden alle Wörter dauerhaft aus Ihrem persönlichen Wörterbuch für {{language}} gelöscht. Diese Aktion kann nicht rückgängig gemacht werden.\",\"confirmClearButton\":\"Ja, Wörterbuch löschen\",\"clearDictionaryWarning\":\"Diese Aktion ist irreversibel.\",\"toastDictionaryWordAdded\":\"Wort '{{word}}' zum Wörterbuch hinzugefügt.\",\"toastDictionaryWordExists\":\"Wort '{{word}}' existiert bereits im Wörterbuch.\",\"toastDictionaryWordEmpty\":\"Leeres Wort kann nicht hinzugefügt werden.\",\"toastDictionaryWordDeleted\":\"Wort '{{word}}' aus dem Wörterbuch gelöscht.\",\"toastDictionaryImportOverwriteSuccess\":\"{{count}} Wörter importiert, Wörterbuch überschrieben.\",\"toastDictionaryImportMergeSuccess\":\"{{count}} neue Wörter importiert und zusammengeführt.\",\"toastDictionaryImportInvalidFormat\":\"Ungültiges Wörterbuchdateiformat. Muss ein JSON-Array von Zeichenfolgen sein.\",\"toastDictionaryImportError\":\"Fehler beim Importieren der Wörterbuchdatei.\",\"toastDictionaryExportSuccess\":\"Wörterbuch erfolgreich exportiert.\",\"toastDictionaryCleared\":\"Persönliches Wörterbuch gelöscht.\",\"toastLanguageSwitched\":\"Schreibsprache automatisch auf {{language}} umgeschaltet.\",\"writingAssistanceTitle\":\"Schreibassistenz\",\"writingAssistanceDescription\":\"Verbessern Sie Ihr Schreiberlebnis mit LinguaFlow, indem Sie anpassen, wie es Sie unterstützt.\",\"yourLanguageProficiencyTitle\":\"Ihre Sprachkompetenz (für die Hauptsprache)\",\"yourLanguageProficiencyDescription\":\"Erhalten Sie sorgfältig zugeschnittene Vorschläge, die Ihrem anspruchsvollen Sprachverständnis entsprechen und einen nuancierteren Ansatz für Ihr Schreiben gewährleisten.\",\"toneDetectionTitle\":\"Tonerkennung\",\"toneDetectionDescription\":\"Tauchen Sie mit unserer Tonerkennungsfunktion in die emotionalen Untertöne Ihres Textes ein, die Ihr Schreiben analysiert und aufschlussreiche Vorschläge zur Verfeinerung und Verbesserung Ihres Tons bietet, damit er bei Ihrem Publikum effektiver ankommt.\",\"nonNativeSupportTitle\":\"Unterstützung für Nicht-Muttersprachler\",\"nonNativeSupportDescription\":\"Profitieren Sie von spezieller Unterstützung für Nicht-Muttersprachler, die durchdachte Anleitungen und praktische Tipps zur Verbesserung Ihrer Sprachgewandtheit und Ihres Selbstvertrauens beim Schreiben bietet.\",\"advancedSettingsTitle\":\"Erweiterte Einstellungen\",\"enableOfflineFunctionalityLabel\":\"Offline-Funktionalität aktivieren\",\"enableOfflineFunctionalityDescription\":\"Grundlegende Funktionen, einschließlich Einstellungen und Wörterbuch, sind offline verfügbar. Wichtige Komponenten werden hinzugefügt, um eine reibungslose Funktion des Programms zu gewährleisten.\",\"enableAutomaticLanguageDetectionLabel\":\"Automatische Spracherkennung aktivieren\",\"enableAutomaticLanguageDetectionDescription\":\"Automatische Erkennung und Umschaltung der Schreibsprache während der Eingabe.\",\"dataManagementLabel\":\"Datenverwaltung\",\"resetAllSettingsLabel\":\"Alle Einstellungen zurücksetzen\",\"resetAllSettingsDescription\":\"Dadurch werden alle Anpassungen, einschließlich Thema, Sprache und Funktionseinstellungen, auf ihre Standardwerte zurückgesetzt. Diese Aktion kann nicht rückgängig gemacht werden.\",\"resetButtonLabel\":\"Zurücksetzen\",\"resetAllSettingsConfirmTitle\":\"Sind Sie sicher, dass Sie alle Einstellungen zurücksetzen möchten?\",\"resetAllSettingsConfirmDescription\":\"Alle Ihre persönlichen Einstellungen, Wörterbuchwörter und Präferenzen werden dauerhaft gelöscht und auf die Anwendungsstandards zurückgesetzt. Diese Aktion kann nicht rückgängig gemacht werden.\",\"confirmResetButton\":\"Ja, alles zurücksetzen\",\"toastResetSuccess\":\"Alle Einstellungen wurden auf die Standardwerte zurückgesetzt. die Anwendung wird jetzt neu geladen.\",\"dictionaryLanguageLabel\":\"Wörterbuchsprache\",\"selectDictionaryLanguagePlaceholder\":\"Sprache zum Anzeigen auswählen...\",\"dictionaryLanguageDescription\":\"Das Wörterbuch für eine bestimmte Sprache anzeigen und verwalten.\",\"toastSuggestionDismissed\":\"Vorschlag verworfen.\",\"dismissButton\":\"Verwerfen\",\"correctButton\":\"Korrigieren\",\"undoButton\":\"Rückgängig\",\"redoButton\":\"Wiederherstellen\",\"aiToolsButton\":\"Intelligente Synonyme\",\"wordToolkitTitle\":\"Synonymvorschlag\",\"wordToolkitDescription\":\"Wählen Sie ein einzelnes Wort im Editor aus, um Synonyme zu erhalten und dessen Aussprache zu hören.\",\"wordToolkitPlaceholder\":\"Wählen Sie ein einzelnes Wort im Haupteditor aus, um intelligente Synonyme zu erhalten.\",\"selectedWordLabel\":\"Ausgewähltes Wort\",\"synonymsLabel\":\"Synonyme\",\"noSynonymsFound\":\"Keine Synonyme gefunden.\",\"applySynonymTooltip\":\"Wort durch '{{synonym}}' ersetzen\",\"toastWordToolkitError\":\"Vorschläge für das ausgewählte Wort konnten nicht abgerufen werden.\",\"toastWordReplacedSuccess\":\"Wort durch '{{word}}' ersetzt.\",\"wordToolkitPopoverDescription\":\"Erhalten Sie Synonyme oder hören Sie die Aussprache des Wortes.\",\"spellingAndPronunciationLabel\":\"Aussprache\",\"pronounceButton\":\"Wort aussprechen\",\"toastPronunciationError\":\"Audio-Aussprache konnte nicht generiert werden.\",\"toastEmptyText\":\"Bitte geben Sie zuerst einen Text ein.\"}"));}}),
"[project]/src/locales/fr.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"appName\":\"LinguaFlow\",\"appDescription\":\"Correction grammaticale et assistant de rédaction\",\"editorTitle\":\"Éditeur\",\"rephraseSelectionButton\":\"Reformuler la sélection\",\"writingStatsTitle\":\"Statistiques d'écriture\",\"wordCountLabel\":\"Nombre de mots\",\"charCountLabel\":\"Nombre de caractères\",\"writingScoreLabel\":\"Score d'écriture\",\"writingScoreUnit\":\"/ 100\",\"writingModeLabel\":\"Mode d'écriture\",\"selectWritingModePlaceholder\":\"Sélectionner le mode d'écriture\",\"formalWritingMode\":\"Formel\",\"casualWritingMode\":\"Décontracté\",\"professionalWritingMode\":\"Professionnel\",\"creativeWritingMode\":\"Créatif\",\"technicalWritingMode\":\"Technique\",\"academicWritingMode\":\"Académique\",\"businessWritingMode\":\"Affaires\",\"aiToneAnalysisAccordionTitle\":\"Analyse de tonalité\",\"aiToneAnalysisTitle\":\"Analyse de tonalité\",\"aiToneAnalysisDescription\":\"Obtenez des commentaires sur la formalité et la confiance de votre écriture.\",\"analyzeToneButton\":\"Analyser la tonalité du texte\",\"formalityLabel\":\"Formalité\",\"confidenceLabel\":\"Confiance\",\"feedbackLabel\":\"Commentaires\",\"writeSomeTextToAnalyzePlaceholder\":\"Écrivez du texte dans l'éditeur pour analyser sa tonalité.\",\"aiTextGenerationAccordionTitle\":\"Génération de Contenu IA\",\"aiTextGenerationTitle\":\"Générateur de Contenu IA\",\"aiTextGenerationDescription\":\"Générez du contenu en fonction de votre invite.\",\"yourPromptLabel\":\"Votre invite\",\"promptPlaceholder\":\"par exemple, Écrivez une nouvelle sur un robot qui découvre la musique\",\"generatedTextLabel\":\"Texte généré\",\"generateTextButton\":\"Générer du texte\",\"settingsAccordionTitle\":\"Paramètres\",\"settingsTitle\":\"Paramètres\",\"settingsDescription\":\"Personnalisez votre expérience LinguaFlow.\",\"themeLabel\":\"Thème\",\"switchToLightMode\":\"Passer en mode clair\",\"switchToDarkMode\":\"Passer en mode sombre\",\"languageLabel\":\"Langue\",\"selectLanguagePlaceholder\":\"Sélectionner la langue\",\"englishUSLanguage\":\"Anglais (États-Unis)\",\"englishUKLanguage\":\"Anglais (Royaume-Uni)\",\"arabicLanguage\":\"العربية (Arabic)\",\"turkishLanguage\":\"Türkçe (Turkish)\",\"spanishLanguage\":\"Español (Spanish)\",\"germanLanguage\":\"Deutsch (German)\",\"frenchLanguage\":\"Français (French)\",\"dutchLanguage\":\"Nederlands (Dutch)\",\"italianLanguage\":\"Italiano (Italian)\",\"startWritingPlaceholder\":\"Commencez à écrire ici...\",\"rephrasePopoverTitle\":\"Reformuler le texte\",\"rephrasePopoverDescription\":\"Examinez la suggestion pour le texte sélectionné.\",\"originalTextLabel\":\"Original\",\"suggestionTextLabel\":\"Suggestion\",\"rephraseWaitMessage\":\"Cliquez sur \\\"Reformuler\\\" ou attendez la suggestion.\",\"applyButton\":\"Appliquer\",\"cancelButton\":\"Annuler\",\"siteHeaderTitle\":\"LinguaFlow\",\"footerText\":\"Créé par Eng: AZA7© 2025. Tous droits réservés®. Votre soutien et votre reconnaissance sont les bienvenus.\",\"toastInputRequiredTitle\":\"Saisie requise\",\"toastEditorEmptyError\":\"L'éditeur est vide. Veuillez écrire du texte à analyser.\",\"toastPromptRequiredError\":\"Veuillez saisir une invite pour générer du texte.\",\"toastSuccessTitle\":\"Succès\",\"toastErrorTitle\":\"Erreur\",\"toastInfoTitle\":\"Info\",\"toastTextGeneratedSuccess\":\"Texte généré avec succès.\",\"toastTextGenerationError\":\"Échec de la génération du texte. Veuillez réessayer.\",\"toastToneAnalysisSuccess\":\"Analyse de tonalité terminée.\",\"toastToneAnalysisError\":\"Échec de l'analyse de la tonalité. Veuillez réessayer.\",\"toastNothingToRephraseError\":\"Rien à reformuler\",\"toastSelectTextToRephraseError\":\"Veuillez sélectionner du texte dans l'éditeur.\",\"toastSuggestionReady\":\"Suggestion prête\",\"toastRephraseError\":\"Échec de la reformulation du texte. Veuillez réessayer.\",\"toastFileUploadedSuccess\":\"Contenu du fichier chargé dans l'éditeur.\",\"toastFileTypeNotSupportedError\":\"Type de fichier non pris en charge. Veuillez télécharger un fichier {{fileType}}.\",\"plagiarismDetectionAccordionTitle\":\"Détection de Plagiat\",\"plagiarismDetectionTitle\":\"Détection de Plagiat\",\"plagiarismDetectionSettingsTitle\":\"Détection de Plagiat\",\"plagiarismDetectionDescription\":\"Protégez votre intégrité en utilisant notre outil de détection de plagiat, conçu pour scanner méticuleusement votre contenu à la recherche de similitudes involontaires avec la littérature existante, vous aidant à maintenir l'originalité de votre écriture.\",\"detectPlagiarismButton\":\"Détecter le Plagiat dans le Texte\",\"originalityScoreLabel\":\"Score d'Originalité\",\"plagiarismReportLabel\":\"Rapport d'Analyse\",\"potentialSourcesFoundLabel\":\"Sources potentielles trouvées\",\"originalSourceLabel\":\"Source originale\",\"similarityScoreLabel\":\"Score de similarité\",\"toastPlagiarismDetectionSuccess\":\"Vérification du plagiat terminée.\",\"toastPlagiarismDetectionError\":\"Échec de la vérification du plagiat. Veuillez réessayer.\",\"writeSomeTextToDetectPlagiarismPlaceholder\":\"Écrivez du texte dans l'éditeur pour vérifier le plagiat.\",\"aiWritingDetectionAccordionTitle\":\"Détection d'Écriture IA\",\"aiWritingDetectionTitle\":\"Détection d'Écriture IA\",\"aiWritingDetectionDescription\":\"Estimez la probabilité que votre texte ait été généré par IA.\",\"detectAiWritingButton\":\"Détecter l'Écriture IA dans le Texte\",\"probabilityAIWrittenLabel\":\"Probabilité Écrit par IA\",\"aiWritingDetectionSummaryLabel\":\"Résumé de l'Analyse\",\"toastAiWritingDetectionSuccess\":\"Détection d'écriture IA terminée.\",\"toastAiWritingDetectionError\":\"Échec de la détection d'écriture IA. Veuillez réessayer.\",\"writeSomeTextToDetectAiWritingPlaceholder\":\"Écrivez du texte dans l'éditeur pour vérifier la paternité IA.\",\"writingSuggestionsTitle\":\"Suggestions d'écriture\",\"analyzingTextDescription\":\"L'IA analyse votre texte pour des suggestions...\",\"suggestionsFoundDescription\":\"{{count}} suggestions trouvées. Examinez-les ci-dessous.\",\"noSuggestionsFoundDescription\":\"Aucune suggestion immédiate trouvée. Continuez à écrire ou essayez de reformuler.\",\"startTypingForSuggestionsDescription\":\"Commencez à taper pour des suggestions d'écriture basées sur l'IA.\",\"suggestionTypeSpelling\":\"Orthographe\",\"suggestionTypeGrammar\":\"Grammaire\",\"suggestionTypeRewrite\":\"Réécriture\",\"suggestionTypeStyle\":\"Style\",\"suggestionTypeUnknown\":\"Suggestion\",\"suggestionLabel\":\"Suggère\",\"applySuggestionButton\":\"Appliquer\",\"suggestionExplanationTooltip\":\"Voir l'explication\",\"toastTextAnalysisError\":\"Impossible d'obtenir des suggestions d'écriture. Veuillez réessayer.\",\"toastSuggestionAppliedSuccess\":\"Suggestion appliquée.\",\"toastSuggestionApplyError\":\"Impossible d'appliquer la suggestion. Le texte original a peut-être changé.\",\"humanizeAiTextAccordionTitle\":\"Humaniser le Texte IA\",\"humanizeAiTextTitle\":\"Humaniser le Texte IA\",\"humanizeAiTextDescription\":\"Réécrire le texte généré par l'IA pour qu'il paraisse plus humain.\",\"humanizeTextButton\":\"Humaniser le Texte\",\"humanizedTextLabel\":\"Texte Humanisé\",\"toastHumanizeTextSuccess\":\"Texte humanisé avec succès.\",\"toastHumanizeTextError\":\"Échec de l'humanisation du texte. Veuillez réessayer.\",\"writeSomeTextToHumanizePlaceholder\":\"Écrivez ou collez du texte généré par l'IA dans l'éditeur pour l'humaniser.\",\"clearEditorButton\":\"Effacer\",\"clearEditorButtonAriaLabel\":\"Effacer tout le texte de l'éditeur\",\"toastEditorClearedSuccess\":\"Contenu de l'éditeur effacé.\",\"generationHistoryTitle\":\"Historique des générations\",\"noGenerationsYetPlaceholder\":\"Aucune génération pour l'instant. Générez du texte pour le voir ici.\",\"promptLabel\":\"Invite\",\"outputLabel\":\"Sortie\",\"useThisPromptButton\":\"Utiliser cette invite\",\"copyOutputButton\":\"Copier la sortie\",\"toastPromptRestoredSuccess\":\"Invite restaurée dans le champ de saisie.\",\"toastTextCopiedSuccess\":\"Texte copié dans le presse-papiers.\",\"toastTextCopyError\":\"Échec de la copie du texte dans le presse-papiers.\",\"insertIntoEditorButton\":\"Insérer dans l'Éditeur\",\"insertIntoEditorButtonTooltip\":\"Ajouter le texte généré à l'éditeur\",\"toastTextInsertedSuccess\":\"Texte généré inséré dans l'éditeur.\",\"copyEditorButton\":\"Copier le Texte\",\"copyEditorButtonAriaLabel\":\"Copier tout le texte de l'éditeur\",\"toastEditorContentCopiedSuccess\":\"Contenu de l'éditeur copié dans le presse-papiers.\",\"toastEditorContentCopyError\":\"Échec de la copie du contenu de l'éditeur dans le presse-papiers.\",\"toastEditorEmptyForCopyError\":\"L'éditeur est vide. Rien à copier.\",\"recordVoiceButtonStart\":\"Enregistrer la voix\",\"recordVoiceButtonStop\":\"Arrêter l'enregistrement\",\"recordVoiceButtonAriaLabelStart\":\"Démarrer l'enregistrement vocal pour transcrire en texte\",\"recordVoiceButtonAriaLabelStop\":\"Arrêter l'enregistrement vocal\",\"toastRecordingStarted\":\"Enregistrement démarré. Parlez dans votre microphone.\",\"toastRecordingStoppedNoTranscript\":\"Enregistrement arrêté. Aucun discours n'a été transcrit.\",\"toastSpeechTranscribedAndAppended\":\"Discours transcrit et ajouté à l'éditeur.\",\"toastSpeechRecognitionNotSupported\":\"La reconnaissance vocale n'est pas prise en charge par votre navigateur.\",\"toastMicrophonePermissionDenied\":\"Permission du microphone refusée. Veuillez l'activer dans les paramètres de votre navigateur et actualiser la page.\",\"toastSpeechNoSpeechDetected\":\"Aucun discours n'a été détecté. Veuillez réessayer.\",\"toastSpeechAudioCaptureError\":\"Erreur de capture audio. Veuillez vérifier votre microphone.\",\"toastSpeechNetworkError\":\"Erreur réseau lors de la reconnaissance vocale. Veuillez vérifier votre connexion.\",\"toastSpeechRecognitionError\":\"Erreur de reconnaissance vocale : {{error}}\",\"toastSpeechServiceNotAllowed\":\"Le service de reconnaissance vocale n'est pas autorisé ou n'est pas disponible. Veuillez réessayer plus tard.\",\"toastSpeechLanguageNotSupportedError\":\"La langue sélectionnée n'est pas prise en charge pour la reconnaissance vocale par votre navigateur.\",\"helpTitle\":\"Aide\",\"helpPanelTitle\":\"Comment utiliser LinguaFlow\",\"helpPanelDescription\":\"Commencez avec les fonctionnalités de LinguaFlow.\",\"helpPanelIntro\":\"Bienvenue sur LinguaFlow ! Ce guide vous aidera à naviguer et à utiliser les puissants outils d'aide à la rédaction de l'application.\",\"helpEditorTitle\":\"L'Éditeur\",\"helpAiToolsTitle\":\"Panneau des Outils IA\",\"helpLanguageSettingsTitle\":\"Paramètres de langue\",\"helpAppearanceSettingsTitle\":\"Paramètres d'apparence\",\"helpDictionarySettingsTitle\":\"Paramètres du dictionnaire\",\"helpFeatureSettingsTitle\":\"Paramètres des fonctionnalités\",\"helpWritingAidSettingsTitle\":\"Paramètres d'aide à la rédaction\",\"helpAdvancedSettingsTitle\":\"Paramètres avancés\",\"helpEditorDescription\":\"L'Éditeur est votre principal espace de travail.<br/><br/><b>- Suggestions en temps réel :</b> Pendant que vous tapez, l'application vérifie automatiquement votre texte et souligne les problèmes potentiels. Cliquez sur un segment surligné pour voir une info-bulle de correction.<br/><b>- Outils IA sur sélection :</b> Sélectionnez n'importe quel morceau de texte pour faire apparaître le bouton <b>Synonymes intelligents</b>. Si vous sélectionnez un seul mot, vous obtiendrez des synonymes et un guide de prononciation. Si vous sélectionnez une phrase plus longue, vous obtiendrez une suggestion de reformulation alimentée par l'IA.<br/><b>- Barre d'outils de formatage :</b> En haut de l'éditeur, vous trouverez des outils pour Annuler/Rétablir, changer les polices et appliquer des formats comme le Gras, l'Italique, les listes et l'alignement du texte.\",\"helpAiToolsDescription\":\"Les panneaux de gauche et de droite fournissent de puissantes capacités d'IA.<br/><br/><b>- Outils d'écriture (Gauche) :</b> Ici, vous pouvez changer le <b>Mode d'écriture</b> pour influencer le style de l'IA, <b>Importer un document</b> ou utiliser le <b>Réécrivain IA</b> pour reformuler tout votre texte. Vous pouvez également utiliser des outils pour <b>Humaniser le texte IA</b>, vérifier l'<b>Écriture IA</b> et détecter le <b>Plagiat</b>.<br/><b>- Outils d'analyse (Droite) :</b> Cette colonne affiche des <b>Statistiques d'écriture</b> en temps réel, fournit un <b>Analyseur de tonalité</b> pour votre texte et inclut le <b>Générateur de contenu IA</b> pour créer un nouveau texte à partir d'une invite. Votre historique de génération est enregistré ici pour une réutilisation facile.\",\"helpLanguageSettingsDescription\":\"Configurez la <b>Langue de l'interface utilisateur</b> pour l'interface de l'application et la <b>Langue d'écriture principale</b> pour l'analyse. Pour certaines langues, vous pouvez également sélectionner un <b>Dialecte régional</b>. Activez la <b>Détection automatique de la langue</b> pour que l'application change de langue d'écriture pendant que vous tapez.\",\"helpAppearanceSettingsDescription\":\"Personnalisez l'apparence de l'application. Choisissez un <b>Thème</b> (Clair, Sombre ou Système), ajustez la <b>Taille de la police</b> globale et activez le <b>Mode contraste élevé</b> pour une meilleure lisibilité.\",\"helpDictionarySettingsDescription\":\"Gérez vos dictionnaires personnels pour différentes langues. <b>Ajoutez</b> des mots que vous utilisez souvent mais qui pourraient être signalés comme des fautes d'orthographe (comme des noms ou du jargon technique). Vous pouvez également <b>Importer</b> ou <b>Exporter</b> votre liste de dictionnaire sous forme de fichier JSON, ou <b>Vider</b> le dictionnaire pour une langue spécifique.\",\"helpFeatureSettingsDescription\":\"Affinez le comportement des fonctionnalités spécifiques de l'IA et de la correction automatique. Ici, vous pouvez activer/désactiver divers outils d'IA générative, les comportements de correction automatique et les fonctionnalités de vérification en temps réel de base pour correspondre à votre flux de travail.\",\"helpWritingAidSettingsDescription\":\"Personnalisez la façon dont l'IA vous assiste. Définissez votre <b>Maîtrise de la langue</b> pour obtenir des suggestions adaptées à votre niveau de compétence. Vous pouvez également activer ou désactiver des fonctionnalités telles que la <b>Détection de la tonalité</b>, la <b>Détection du plagiat</b> et un soutien spécialisé pour les <b>Locuteurs non natifs</b>.\",\"helpAdvancedSettingsDescription\":\"Contrôlez les comportements opérationnels de base. Activez la <b>Fonctionnalité hors ligne</b> pour utiliser les fonctionnalités de base sans connexion Internet. Si vous souhaitez repartir de zéro, vous pouvez <b>Réinitialiser tous les paramètres</b> pour restaurer l'application à ses valeurs par défaut d'origine (cette action est irréversible).\",\"helpPanelTip\":\"Expérimentez avec différents outils et paramètres pour trouver ce qui convient le mieux à votre style d'écriture et à vos besoins !\",\"Write Tools\":\"Outils d'écriture\",\"Import Document\":\"Importer un document\",\"Quick Action\":\"Action rapide\",\"Tone Analyzer\":\"Analyseur de tonalité\",\"aiRewriteAccordionTitle\":\"Réécrivain IA\",\"aiRewriteTitle\":\"Réécrivain IA\",\"aiRewriteDescription\":\"Réécrivez l'intégralité du contenu de l'éditeur pour améliorer la clarté et le style.\",\"rewriteEditorContentButton\":\"Réécrire le contenu de l'éditeur\",\"rewrittenTextLabel\":\"Texte réécrit\",\"applyToEditorButton\":\"Appliquer à l'éditeur\",\"toastRewriteSuccess\":\"Contenu de l'éditeur réécrit avec succès.\",\"toastRewriteError\":\"Échec de la réécriture du contenu de l'éditeur. Veuillez réessayer.\",\"writeSomeTextToRewritePlaceholder\":\"Écrivez du texte dans l'éditeur pour le réécrire.\",\"Click the button to rewrite the editor content.\":\"Cliquez sur le bouton pour réécrire le contenu de l'éditeur.\",\"dropzoneInstruction\":\"Déposez les fichiers ici ou parcourez\",\"toastFileImportSuccessTitle\":\"Fichier importé\",\"toastFileImportSuccessMessage\":\"Le contenu du document a été chargé.\",\"toastFileImportErrorTitle\":\"Erreur d'importation\",\"toastFileImportErrorMessage\":\"Impossible de lire le contenu du fichier. Veuillez vous assurer qu'il s'agit d'un fichier .txt valide.\",\"toastInvalidFileTypeMessage\":\"Type de fichier invalide. Seuls les fichiers .txt sont acceptés.\",\"dropzoneAriaLabel\":\"Zone de dépôt pour l'importation de documents : Cliquez ou glissez-déposez un fichier .txt pour le télécharger.\",\"featuresLabel\":\"Fonctionnalités\",\"featureSettingsDescription\":\"Personnalisez la fonctionnalité des fonctionnalités d'aide à la rédaction spécifiques.\",\"appearanceLabel\":\"Apparence\",\"writingAidLabel\":\"Aide à la rédaction\",\"dictionaryLabel\":\"Dictionnaire\",\"dictionarySettingsDescription\":\"Ajouter des mots personnalisés ou gérer des dictionnaires personnels. (Espace réservé)\",\"advancedSettingsLabel\":\"Avancé\",\"advancedSettingsDescription\":\"Accéder aux options de configuration avancées. Utiliser avec prudence. (Espace réservé)\",\"uiLanguageLabel\":\"Langue de l'interface utilisateur\",\"selectUiLanguagePlaceholder\":\"Sélectionner la langue de l'interface...\",\"uiLanguageDescription\":\"Modifie la langue de l'interface de l'application.\",\"writingLanguageLabel\":\"Langue d'écriture principale\",\"selectWritingLanguagePlaceholder\":\"Sélectionner la langue d'écriture...\",\"writingLanguageDescription\":\"Définit la langue principale pour l'analyse et la génération par l'IA.\",\"regionalDialectLabel\":\"Dialecte régional\",\"selectRegionalDialectPlaceholder\":\"Sélectionner le dialecte...\",\"regionalDialectDescription\":\"Spécifie la variation régionale pour la langue d'écriture sélectionnée.\",\"languageProficiencyLabel\":\"Maîtrise de la langue\",\"selectProficiencyPlaceholder\":\"Sélectionner la maîtrise...\",\"languageProficiencyDescription\":\"Aide l'IA à adapter les suggestions à votre niveau de compétence linguistique.\",\"proficiencyNative\":\"Natif\",\"proficiencyAdvanced\":\"Avancé (C1/C2)\",\"proficiencyIntermediate\":\"Intermédiaire (B1/B2)\",\"proficiencyBeginner\":\"Débutant (A1/A2)\",\"languageEnglishGeneral\":\"Anglais\",\"languageSpanishGeneral\":\"Espagnol\",\"languageFrenchGeneral\":\"Français\",\"languageGermanGeneral\":\"Allemand\",\"languageItalianGeneral\":\"Italien\",\"languageDutchGeneral\":\"Néerlandais\",\"languageArabicGeneral\":\"Arabe\",\"arabicSyriaLanguage\":\"Arabe (Syrie)\",\"arabicSaudiArabiaLanguage\":\"Arabe (Arabie saoudite)\",\"arabicEgyptLanguage\":\"Arabe (Égypte)\",\"languageTurkishGeneral\":\"Turc\",\"spanishSpainLanguage\":\"Espagnol (Espagne)\",\"spanishMexicoLanguage\":\"Espagnol (Mexique)\",\"themeLight\":\"Clair\",\"themeDark\":\"Sombre\",\"themeSystem\":\"Par défaut du système\",\"selectThemePlaceholder\":\"Sélectionner le thème...\",\"themeDescription\":\"Choisissez le thème visuel de l'application.\",\"fontSizeLabel\":\"Taille de la police\",\"selectFontSizePlaceholder\":\"Sélectionner la taille de la police...\",\"fontSizeSmall\":\"Petite\",\"fontSizeMedium\":\"Moyenne\",\"fontSizeLarge\":\"Grande\",\"fontSizeDescription\":\"Ajustez la taille du texte dans toute l'application.\",\"highContrastModeLabel\":\"Mode contraste élevé\",\"highContrastModeDescription\":\"Augmente le contraste texte/arrière-plan pour une meilleure lisibilité.\",\"enabledLabel\":\"Activé\",\"disabledLabel\":\"Désactivé\",\"personalDictionaryLabel\":\"Dictionnaire personnel\",\"personalDictionaryDescription\":\"Ajoutez des mots que vous utilisez frequently et qui pourraient être signalés comme des erreurs.\",\"addWordPlaceholder\":\"Entrez un mot...\",\"addWordButton\":\"Ajouter un mot\",\"deleteWordButtonAria\":\"Supprimer le mot {{word}}\",\"dictionaryEmptyPlaceholder\":\"Votre dictionnaire est vide. Ajoutez des mots !\",\"dictionaryImportExportLabel\":\"Importer / Exporter le dictionnaire\",\"importDictionaryButton\":\"Importer\",\"exportDictionaryButton\":\"Exporter\",\"dictionaryImportExportDescription\":\"Sauvegardez ou partagez votre dictionnaire personnel sous forme de fichier JSON.\",\"clearDictionaryForLanguageButton\":\"Vider le dictionnaire {{language}}\",\"clearDictionaryConfirmTitle\":\"Êtes-vous sûr ?\",\"clearDictionaryForLanguageConfirmDescription\":\"Cela supprimera définitivement tous les mots de votre dictionnaire personnel pour {{language}}. Cette action est irréversible.\",\"confirmClearButton\":\"Oui, vider le dictionnaire\",\"clearDictionaryWarning\":\"Cette action est irréversible.\",\"toastDictionaryWordAdded\":\"Mot '{{word}}' ajouté au dictionnaire.\",\"toastDictionaryWordExists\":\"Le mot '{{word}}' existe déjà dans le dictionnaire.\",\"toastDictionaryWordEmpty\":\"Impossible d'ajouter un mot vide.\",\"toastDictionaryWordDeleted\":\"Mot '{{word}}' supprimé du dictionnaire.\",\"toastDictionaryImportOverwriteSuccess\":\"{{count}} mots importés, dictionnaire écrasé.\",\"toastDictionaryImportMergeSuccess\":\"{{count}} nouveaux mots importés et fusionnés.\",\"toastDictionaryImportInvalidFormat\":\"Format de fichier de dictionnaire non valide. Doit être un tableau JSON de chaînes.\",\"toastDictionaryImportError\":\"Erreur lors de l'importation du fichier de dictionnaire.\",\"toastDictionaryExportSuccess\":\"Dictionnaire exporté avec succès.\",\"toastDictionaryCleared\":\"Dictionnaire personnel vidé.\",\"toastLanguageSwitched\":\"Langue d'écriture changée automatiquement en {{language}}.\",\"writingAssistanceTitle\":\"Assistance à la rédaction\",\"writingAssistanceDescription\":\"Améliorez votre expérience d'écriture avec LinguaFlow en personnalisant son assistance.\",\"yourLanguageProficiencyTitle\":\"Votre maîtrise de la langue (pour la langue principale)\",\"yourLanguageProficiencyDescription\":\"Recevez des suggestions méticuleusement adaptées qui correspondent à votre compréhension sophistiquée de la langue, garantissant une approche plus nuancée de votre écriture.\",\"toneDetectionTitle\":\"Détection de la tonalité\",\"toneDetectionDescription\":\"Explorez les sous-entendus émotionnels de votre texte avec notre fonction de détection de la tonalité, qui analyse votre écriture et propose des suggestions pertinentes pour affiner et élever votre ton, le rendant plus efficace auprès de votre public.\",\"nonNativeSupportTitle\":\"Soutien pour les locuteurs non natifs\",\"nonNativeSupportDescription\":\"Bénéficiez d'une assistance spécialisée destinée aux locuteurs non natifs, offrant des conseils réfléchis et des astuces pratiques pour améliorer votre fluidité et votre confiance en l'écriture.\",\"advancedSettingsTitle\":\"Paramètres avancés\",\"enableOfflineFunctionalityLabel\":\"Activer la fonctionnalité hors ligne\",\"enableOfflineFunctionalityDescription\":\"Les fonctionnalités de base, y compris les paramètres et le dictionnaire, sont disponibles hors ligne. Des composants clés seront inclus pour assurer un fonctionnement fluide du programme.\",\"enableAutomaticLanguageDetectionLabel\":\"Activer la détection automatique de la langue\",\"enableAutomaticLanguageDetectionDescription\":\"Détecte et change automatiquement la langue d'écriture pendant que vous tapez.\",\"dataManagementLabel\":\"Gestion des données\",\"resetAllSettingsLabel\":\"Réinitialiser tous les paramètres\",\"resetAllSettingsDescription\":\"Cela réinitialisera toutes les personnalisations, y compris le thème, la langue et les paramètres des fonctionnalités, à leurs valeurs par défaut. Cette action est irréversible.\",\"resetButtonLabel\":\"Réinitialiser\",\"resetAllSettingsConfirmTitle\":\"Êtes-vous sûr de vouloir réinitialiser tous les paramètres ?\",\"resetAllSettingsConfirmDescription\":\"Tous vos paramètres personnels, mots du dictionnaire et préférences seront supprimés de manière permanente et réinitialisés aux valeurs par défaut de l'application. Cette action est irréversible.\",\"confirmResetButton\":\"Oui, tout réinitialiser\",\"toastResetSuccess\":\"Tous les paramètres ont été réinitialisés par défaut. L'application va maintenant se recharger.\",\"dictionaryLanguageLabel\":\"Langue du dictionnaire\",\"selectDictionaryLanguagePlaceholder\":\"Sélectionner la langue à afficher...\",\"dictionaryLanguageDescription\":\"Affichez et gérez le dictionnaire pour une langue spécifique.\",\"toastSuggestionDismissed\":\"Suggestion rejetée.\",\"dismissButton\":\"Rejeter\",\"correctButton\":\"Corriger\",\"undoButton\":\"Annuler\",\"redoButton\":\"Rétablir\",\"aiToolsButton\":\"Synonymes intelligents\",\"wordToolkitTitle\":\"Suggestion de synonymes\",\"wordToolkitDescription\":\"Sélectionnez un seul mot dans l'éditeur pour obtenir des synonymes et entendre sa prononciation.\",\"wordToolkitPlaceholder\":\"Sélectionnez un seul mot dans l'éditeur principal pour obtenir des synonymes intelligents.\",\"selectedWordLabel\":\"Mot sélectionné\",\"synonymsLabel\":\"Synonymes\",\"noSynonymsFound\":\"Aucun synonyme trouvé.\",\"applySynonymTooltip\":\"Remplacer le mot par '{{synonym}}'\",\"toastWordToolkitError\":\"Impossible d'obtenir des suggestions pour le mot sélectionné.\",\"toastWordReplacedSuccess\":\"Mot remplacé par '{{word}}'.\",\"wordToolkitPopoverDescription\":\"Obtenez des synonymes ou entendez la prononciation du mot.\",\"spellingAndPronunciationLabel\":\"Prononciation\",\"pronounceButton\":\"Prononcer le mot\",\"toastPronunciationError\":\"Échec de la génération de la prononciation audio.\",\"toastEmptyText\":\"Veuillez d'abord entrer du texte.\"}"));}}),
"[project]/src/locales/nl.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"appName\":\"LinguaFlow\",\"appDescription\":\"Grammaticacorrectie & Schrijfassistent\",\"editorTitle\":\"Editor\",\"rephraseSelectionButton\":\"Selectie herformuleren\",\"writingStatsTitle\":\"Schrijfstatistieken\",\"wordCountLabel\":\"Woordenaantal\",\"charCountLabel\":\"Tekenaantal\",\"writingScoreLabel\":\"Schrijfscore\",\"writingScoreUnit\":\"/ 100\",\"writingModeLabel\":\"Schrijfmodus\",\"selectWritingModePlaceholder\":\"Selecteer schrijfmodus\",\"formalWritingMode\":\"Formeel\",\"casualWritingMode\":\"Informeel\",\"professionalWritingMode\":\"Professioneel\",\"creativeWritingMode\":\"Creatief\",\"technicalWritingMode\":\"Technisch\",\"academicWritingMode\":\"Academisch\",\"businessWritingMode\":\"Zakelijk\",\"aiToneAnalysisAccordionTitle\":\"Toonanalyse\",\"aiToneAnalysisTitle\":\"Toonanalyse\",\"aiToneAnalysisDescription\":\"Krijg feedback over de formaliteit en stelligheid van je tekst.\",\"analyzeToneButton\":\"Teksttoon Analyseren\",\"formalityLabel\":\"Formaliteit\",\"confidenceLabel\":\"Stelligheid\",\"feedbackLabel\":\"Feedback\",\"writeSomeTextToAnalyzePlaceholder\":\"Schrijf wat tekst in de editor om de toon te analyseren.\",\"aiTextGenerationAccordionTitle\":\"Contentgeneratie\",\"aiTextGenerationTitle\":\"Contentgenerator\",\"aiTextGenerationDescription\":\"Genereer inhoud op basis van je prompt.\",\"yourPromptLabel\":\"Je prompt\",\"promptPlaceholder\":\"bijv. Schrijf een kort verhaal over een robot die muziek ontdekt\",\"generatedTextLabel\":\"Gegenereerde tekst\",\"generateTextButton\":\"Genereer tekst\",\"settingsAccordionTitle\":\"Instellingen\",\"settingsTitle\":\"Instellingen\",\"settingsDescription\":\"Personaliseer je LinguaFlow ervaring.\",\"themeLabel\":\"Thema\",\"switchToLightMode\":\"Schakel naar lichte modus\",\"switchToDarkMode\":\"Schakel naar donkere modus\",\"languageLabel\":\"Taal\",\"selectLanguagePlaceholder\":\"Selecteer taal\",\"englishUSLanguage\":\"Engels (VS)\",\"englishUKLanguage\":\"Engels (VK)\",\"arabicLanguage\":\"العربية (Arabic)\",\"turkishLanguage\":\"Türkçe (Turkish)\",\"spanishLanguage\":\"Español (Spanish)\",\"germanLanguage\":\"Deutsch (German)\",\"frenchLanguage\":\"Français (French)\",\"dutchLanguage\":\"Nederlands (Dutch)\",\"italianLanguage\":\"Italiano (Italian)\",\"startWritingPlaceholder\":\"Begin hier met schrijven...\",\"rephrasePopoverTitle\":\"Tekst herformuleren\",\"rephrasePopoverDescription\":\"Bekijk de suggestie voor je geselecteerde tekst.\",\"originalTextLabel\":\"Origineel\",\"suggestionTextLabel\":\"Suggestie\",\"rephraseWaitMessage\":\"Klik op \\\"Herformuleren\\\" of wacht op de suggestie.\",\"applyButton\":\"Toepassen\",\"cancelButton\":\"Annuleren\",\"siteHeaderTitle\":\"LinguaFlow\",\"footerText\":\"Gemaakt door Eng: AZA7© 2025. Alle rechten voorbehouden®. Uw steun en erkenning worden zeer op prijs gesteld.\",\"toastInputRequiredTitle\":\"Invoer vereist\",\"toastEditorEmptyError\":\"Editor is leeg. Schrijf wat tekst om te analyseren.\",\"toastPromptRequiredError\":\"Voer een prompt in om tekst te genereren.\",\"toastSuccessTitle\":\"Geslaagd\",\"toastErrorTitle\":\"Fout\",\"toastInfoTitle\":\"Info\",\"toastTextGeneratedSuccess\":\"Tekst succesvol gegenereerd.\",\"toastTextGenerationError\":\"Kon tekst niet genereren. Probeer het opnieuw.\",\"toastToneAnalysisSuccess\":\"Toonanalyse voltooid.\",\"toastToneAnalysisError\":\"Kon toon niet analyseren. Probeer het opnieuw.\",\"toastNothingToRephraseError\":\"Niets om te herformuleren\",\"toastSelectTextToRephraseError\":\"Selecteer wat tekst in de editor.\",\"toastSuggestionReady\":\"Suggestie gereed\",\"toastRephraseError\":\"Kon tekst niet herformuleren. Probeer het opnieuw.\",\"toastFileUploadedSuccess\":\"Bestandsinhoud geladen in editor.\",\"toastFileTypeNotSupportedError\":\"Bestandstype niet ondersteund. Upload een {{fileType}} bestand.\",\"plagiarismDetectionAccordionTitle\":\"Plagiaatdetectie\",\"plagiarismDetectionTitle\":\"Plagiaatdetectie\",\"plagiarismDetectionSettingsTitle\":\"Plagiaatdetectie\",\"plagiarismDetectionDescription\":\"Bescherm uw integriteit door gebruik te maken van onze plagiaatdetectietool, die is ontworpen om uw inhoud nauwgezet te scannen op onbedoelde overeenkomsten met bestaande literatuur, zodat u de originaliteit van uw schrijven kunt behouden.\",\"detectPlagiarismButton\":\"Detecteer Plagiaat Tekst\",\"originalityScoreLabel\":\"Originaliteitsscore\",\"plagiarismReportLabel\":\"Analyserapport\",\"potentialSourcesFoundLabel\":\"Mogelijke bronnen gevonden\",\"originalSourceLabel\":\"Oorspronkelijke bron\",\"similarityScoreLabel\":\"Overeenkomstscore\",\"toastPlagiarismDetectionSuccess\":\"Plagiaatcontrole voltooid.\",\"toastPlagiarismDetectionError\":\"Kon plagiaat niet controleren. Probeer het opnieuw.\",\"writeSomeTextToDetectPlagiarismPlaceholder\":\"Schrijf wat tekst in de editor om op plagiaat te controleren.\",\"aiWritingDetectionAccordionTitle\":\"AI Schrijfdetectie\",\"aiWritingDetectionTitle\":\"AI Schrijfdetectie\",\"aiWritingDetectionDescription\":\"Schat de waarschijnlijkheid in dat uw tekst door AI is gegenereerd.\",\"detectAiWritingButton\":\"Detecteer AI Schrijven Tekst\",\"probabilityAIWrittenLabel\":\"Waarschijnlijkheid AI Geschreven\",\"aiWritingDetectionSummaryLabel\":\"Analyse Samenvatting\",\"toastAiWritingDetectionSuccess\":\"AI schrijfdetectie voltooid.\",\"toastAiWritingDetectionError\":\"Kon AI schrijven niet detecteren. Probeer het opnieuw.\",\"writeSomeTextToDetectAiWritingPlaceholder\":\"Schrijf wat tekst in de editor om te controleren op AI-auteurschap.\",\"writingSuggestionsTitle\":\"Schrijfsuggesties\",\"analyzingTextDescription\":\"AI analyseert uw tekst op suggesties...\",\"suggestionsFoundDescription\":\"{{count}} suggesties gevonden. Bekijk ze hieronder.\",\"noSuggestionsFoundDescription\":\"Geen directe suggesties gevonden. Blijf schrijven of probeer te herformuleren.\",\"startTypingForSuggestionsDescription\":\"Begin met typen voor AI-gestuurde schrijfsuggesties.\",\"suggestionTypeSpelling\":\"Spelling\",\"suggestionTypeGrammar\":\"Grammatica\",\"suggestionTypeRewrite\":\"Herschrijven\",\"suggestionTypeStyle\":\"Stijl\",\"suggestionTypeUnknown\":\"Suggestie\",\"suggestionLabel\":\"Suggereert\",\"applySuggestionButton\":\"Toepassen\",\"suggestionExplanationTooltip\":\"Uitleg bekijken\",\"toastTextAnalysisError\":\"Kon schrijfsuggesties niet ophalen. Probeer het opnieuw.\",\"toastSuggestionAppliedSuccess\":\"Suggestie toegepast.\",\"toastSuggestionApplyError\":\"Kon suggestie niet toepassen. De originele tekst is mogelijk gewijzigd.\",\"humanizeAiTextAccordionTitle\":\"AI Tekst Vermenselijken\",\"humanizeAiTextTitle\":\"AI Tekst Vermenselijken\",\"humanizeAiTextDescription\":\"AI-gegenereerde tekst herschrijven zodat het menselijker klinkt.\",\"humanizeTextButton\":\"Tekst Vermenselijken\",\"humanizedTextLabel\":\"Vermenselijkte Tekst\",\"toastHumanizeTextSuccess\":\"Tekst succesvol vermenselijkt.\",\"toastHumanizeTextError\":\"Kon tekst niet vermenselijken. Probeer het opnieuw.\",\"writeSomeTextToHumanizePlaceholder\":\"Schrijf of plak AI-gegenereerde tekst in de editor om het te vermenselijken.\",\"clearEditorButton\":\"Wissen\",\"clearEditorButtonAriaLabel\":\"Wis alle tekst uit de editor\",\"toastEditorClearedSuccess\":\"Editorinhoud gewist.\",\"generationHistoryTitle\":\"Generatiegeschiedenis\",\"noGenerationsYetPlaceholder\":\"Nog geen generaties. Genereer wat tekst om het hier te zien.\",\"promptLabel\":\"Prompt\",\"outputLabel\":\"Uitvoer\",\"useThisPromptButton\":\"Gebruik deze Prompt\",\"copyOutputButton\":\"Kopieer Uitvoer\",\"toastPromptRestoredSuccess\":\"Prompt hersteld naar invoerveld.\",\"toastTextCopiedSuccess\":\"Tekst gekopieerd naar klembord.\",\"toastTextCopyError\":\"Kon tekst niet naar klembord kopiëren.\",\"insertIntoEditorButton\":\"Invoegen in Editor\",\"insertIntoEditorButtonTooltip\":\"Gegenereerde tekst aan editor toevoegen\",\"toastTextInsertedSuccess\":\"Gegenereerde tekst ingevoegd in editor.\",\"copyEditorButton\":\"Tekst kopiëren\",\"copyEditorButtonAriaLabel\":\"Alle tekst uit de editor kopiëren\",\"toastEditorContentCopiedSuccess\":\"Editorinhoud gekopieerd naar klembord.\",\"toastEditorContentCopyError\":\"Kon editorinhoud niet naar klembord kopiëren.\",\"toastEditorEmptyForCopyError\":\"Editor is leeg. Niets om te kopiëren.\",\"recordVoiceButtonStart\":\"Spraak opnemen\",\"recordVoiceButtonStop\":\"Opname stoppen\",\"recordVoiceButtonAriaLabelStart\":\"Start spraakopname om naar tekst te transcriberen\",\"recordVoiceButtonAriaLabelStop\":\"Stop spraakopname\",\"toastRecordingStarted\":\"Opname gestart. Spreek in uw microfoon.\",\"toastRecordingStoppedNoTranscript\":\"Opname gestopt. Er is geen spraak getranscribeerd.\",\"toastSpeechTranscribedAndAppended\":\"Spraak getranscribeerd en aan editor toegevoegd.\",\"toastSpeechRecognitionNotSupported\":\"Spraakherkenning wordt niet ondersteund door uw browser.\",\"toastMicrophonePermissionDenied\":\"Microfoontoestemming geweigerd. Schakel deze in uw browserinstellingen in en vernieuw de pagina.\",\"toastSpeechNoSpeechDetected\":\"Geen spraak gedetecteerd. Probeer het opnieuw.\",\"toastSpeechAudioCaptureError\":\"Fout bij audio-opname. Controleer uw microfoon.\",\"toastSpeechNetworkError\":\"Netwerkfout tijdens spraakherkenning. Controleer uw verbinding.\",\"toastSpeechRecognitionError\":\"Fout bij spraakherkenning: {{error}}\",\"toastSpeechServiceNotAllowed\":\"Spraakherkenningsservice is niet toegestaan of niet beschikbaar. Probeer het later opnieuw.\",\"toastSpeechLanguageNotSupportedError\":\"De geselecteerde taal wordt niet ondersteund voor spraakherkenning door uw browser.\",\"helpTitle\":\"Help\",\"helpPanelTitle\":\"Hoe LinguaFlow te gebruiken\",\"helpPanelDescription\":\"Ga aan de slag met de functies van LinguaFlow.\",\"helpPanelIntro\":\"Welkom bij LinguaFlow! Deze gids helpt u bij het navigeren en gebruiken van de krachtige schrijfhulpmiddelen van de applicatie.\",\"helpEditorTitle\":\"De Editor\",\"helpAiToolsTitle\":\"AI Tools Paneel\",\"helpLanguageSettingsTitle\":\"Taalinstellingen\",\"helpAppearanceSettingsTitle\":\"Uiterlijkinstellingen\",\"helpDictionarySettingsTitle\":\"Woordenboekinstellingen\",\"helpFeatureSettingsTitle\":\"Functie-instellingen\",\"helpWritingAidSettingsTitle\":\"Schrijfhulpinstellingen\",\"helpAdvancedSettingsTitle\":\"Geavanceerde instellingen\",\"helpEditorDescription\":\"De Editor is uw belangrijkste werkruimte.<br/><br/><b>- Realtime suggesties:</b> Terwijl u typt, controleert de app automatisch uw tekst en onderstreept mogelijke problemen. Klik op een gemarkeerd segment om een correctiepop-up te zien.<br/><b>- AI-tools bij selectie:</b> Selecteer een stuk tekst om de knop <b>Slimme synoniemen</b> weer te geven. Als u één woord selecteert, krijgt u synoniemen en een uitspraakgids. Als u een langere zin selecteert, krijgt u een door AI aangedreven herformuleringssuggestie.<br/><b>- Opmaakwerkbalk:</b> Bovenaan de editor vindt u hulpmiddelen om Ongedaan te maken/Opnieuw uit te voeren, lettertypen te wijzigen en opmaak toe te passen zoals Vet, Cursief, lijsten en tekstuitlijning.\",\"helpAiToolsDescription\":\"De panelen aan de linker- en rechterkant bieden krachtige AI-mogelijkheden.<br/><br/><b>- Schrijfhulpmiddelen (Links):</b> Hier kunt u de <b>Schrijfmodus</b> wijzigen om de stijl van de AI te beïnvloeden, een <b>Document importeren</b> of de <b>AI Herschrijver</b> gebruiken om uw volledige tekst opnieuw op te stellen. U kunt ook hulpmiddelen gebruiken om <b>AI-tekst te vermenselijken</b>, te controleren op <b>AI-schrijven</b> en <b>Plagiaat</b> te detecteren.<br/><b>- Analysehulpmiddelen (Rechts):</b> Deze kolom toont realtime <b>Schrijfstatistieken</b>, biedt een <b>Toonanalysator</b> voor uw tekst en bevat de <b>AI-inhoudsgenerator</b> om nieuwe tekst te maken op basis van een prompt. Uw generatiegeschiedenis wordt hier opgeslagen voor eenvoudig hergebruik.\",\"helpLanguageSettingsDescription\":\"Configureer de <b>UI-taal</b> voor de applicatie-interface en de <b>Primaire schrijftaal</b> voor analyse. Voor sommige talen kunt u ook een <b>Regionaal dialect</b> selecteren. Schakel <b>Automatische taalherkenning</b> in om de app van schrijftaal te laten wisselen terwijl u typt.\",\"helpAppearanceSettingsDescription\":\"Pas het uiterlijk van de app aan. Kies een <b>Thema</b> (Licht, Donker of Systeem), pas de algemene <b>Lettergrootte</b> aan en schakel de <b>Hoge contrastmodus</b> in voor betere leesbaarheid.\",\"helpDictionarySettingsDescription\":\"Beheer uw persoonlijke woordenboeken voor verschillende talen. <b>Voeg</b> woorden toe die u vaak gebruikt, maar die mogelijk als spelfouten worden gemarkeerd (zoals namen of jargon). U kunt uw woordenboeklijst ook <b>importeren</b> of <b>exporteren</b> als een JSON-bestand, of het woordenboek voor een specifieke taal <b>wissen</b>.\",\"helpFeatureSettingsDescription\":\"Pas het gedrag van specifieke AI- en autcorrectiefuncties aan. Hier kunt u verschillende generatieve AI-tools, autcorrectiegedragingen en de belangrijkste realtime controlefunctionaliteiten in- en uitschakelen om aan uw workflow te voldoen.\",\"helpWritingAidSettingsDescription\":\"Pas aan hoe de AI u helpt. Stel uw <b>Taalvaardigheid</b> in om suggesties te krijgen die zijn afgestemd op uw vaardigheidsniveau. U kunt ook functies zoals <b>Toondetectie</b>, <b>Plagiaatdetectie</b> en gespecialiseerde ondersteuning voor <b>Niet-moedertaalsprekers</b> in- of uitschakelen.\",\"helpAdvancedSettingsDescription\":\"Beheer belangrijke operationele gedragingen. Schakel <b>Offline functionaliteit</b> in om basisfuncties zonder internetverbinding te gebruiken. Als u ooit opnieuw wilt beginnen, kunt u <b>Alle instellingen resetten</b> om de applicatie terug te zetten naar de oorspronkelijke standaardinstellingen (dit kan niet ongedaan worden gemaakt).\",\"helpPanelTip\":\"Experimenteer met verschillende tools en instellingen om te ontdekken wat het beste werkt voor uw schrijfstijl en behoeften!\",\"Write Tools\":\"Schrijfhulpmiddelen\",\"Import Document\":\"Document importeren\",\"Quick Action\":\"Snelle actie\",\"Tone Analyzer\":\"Toonanalysator\",\"aiRewriteAccordionTitle\":\"AI Herschrijver\",\"aiRewriteTitle\":\"AI Herschrijver\",\"aiRewriteDescription\":\"Herschrijf de volledige editorinhoud om de duidelijkheid en stijl te verbeteren.\",\"rewriteEditorContentButton\":\"Editorinhoud herschrijven\",\"rewrittenTextLabel\":\"Herschreven tekst\",\"applyToEditorButton\":\"Toepassen op Editor\",\"toastRewriteSuccess\":\"Editorinhoud succesvol herschreven.\",\"toastRewriteError\":\"Kon editorinhoud niet herschrijven. Probeer het opnieuw.\",\"writeSomeTextToRewritePlaceholder\":\"Schrijf wat tekst in de editor om het te herschrijven.\",\"Click the button to rewrite the editor content.\":\"Klik op de knop om de editorinhoud te herschrijven.\",\"dropzoneInstruction\":\"Sleep bestanden hierheen of blader\",\"toastFileImportSuccessTitle\":\"Bestand geïmporteerd\",\"toastFileImportSuccessMessage\":\"Documentinhoud is geladen.\",\"toastFileImportErrorTitle\":\"Importfout\",\"toastFileImportErrorMessage\":\"Kon de bestandsinhoud niet lezen. Zorg ervoor dat het een geldig .txt-bestand is.\",\"toastInvalidFileTypeMessage\":\"Ongeldig bestandstype. Alleen .txt-bestanden worden geaccepteerd.\",\"dropzoneAriaLabel\":\"Dropzone voor documentimport: Klik of sleep een .txt-bestand hierheen om te uploaden.\",\"featuresLabel\":\"Functies\",\"featureSettingsDescription\":\"Past de functionaliteit van specifieke schrijfhulpfuncties aan.\",\"appearanceLabel\":\"Uiterlijk\",\"writingAidLabel\":\"Schrijfhulp\",\"dictionaryLabel\":\"Woordenboek\",\"dictionarySettingsDescription\":\"Voeg aangepaste woorden toe of beheer persoonlijke woordenboeken. (Placeholder)\",\"advancedSettingsLabel\":\"Geavanceerd\",\"advancedSettingsDescription\":\"Toegang tot geavanceerde configuratieopties. Wees voorzichtig. (Placeholder)\",\"uiLanguageLabel\":\"UI Taal\",\"selectUiLanguagePlaceholder\":\"Selecteer UI taal...\",\"uiLanguageDescription\":\"Verandert de taal van de applicatie-interface.\",\"writingLanguageLabel\":\"Primaire Schrijftaal\",\"selectWritingLanguagePlaceholder\":\"Selecteer schrijftaal...\",\"writingLanguageDescription\":\"Stelt de primaire taal in voor AI-analyse en -generatie.\",\"regionalDialectLabel\":\"Regionaal Dialect\",\"selectRegionalDialectPlaceholder\":\"Selecteer dialect...\",\"regionalDialectDescription\":\"Specificeert de regionale variatie voor de geselecteerde schrijftaal.\",\"languageProficiencyLabel\":\"Taalvaardigheid\",\"selectProficiencyPlaceholder\":\"Selecteer vaardigheid...\",\"languageProficiencyDescription\":\"Helpt AI suggesties af te stemmen op uw taalvaardigheidsniveau.\",\"proficiencyNative\":\"Moedertaal\",\"proficiencyAdvanced\":\"Gevorderd (C1/C2)\",\"proficiencyIntermediate\":\"Gemiddeld (B1/B2)\",\"proficiencyBeginner\":\"Beginner (A1/A2)\",\"languageEnglishGeneral\":\"Engels\",\"languageSpanishGeneral\":\"Spaans\",\"languageFrenchGeneral\":\"Frans\",\"languageGermanGeneral\":\"Duits\",\"languageItalianGeneral\":\"Italiaans\",\"languageDutchGeneral\":\"Nederlands\",\"languageArabicGeneral\":\"Arabisch\",\"arabicSyriaLanguage\":\"Arabisch (Syrië)\",\"arabicSaudiArabiaLanguage\":\"Arabisch (Saoedi-Arabië)\",\"arabicEgyptLanguage\":\"Arabisch (Egypte)\",\"languageTurkishGeneral\":\"Turks\",\"spanishSpainLanguage\":\"Spaans (Spanje)\",\"spanishMexicoLanguage\":\"Spaans (Mexico)\",\"themeLight\":\"Licht\",\"themeDark\":\"Donker\",\"themeSystem\":\"Systeemstandaard\",\"selectThemePlaceholder\":\"Selecteer thema...\",\"themeDescription\":\"Kies het visuele thema van de applicatie.\",\"fontSizeLabel\":\"Lettergrootte\",\"selectFontSizePlaceholder\":\"Selecteer lettergrootte...\",\"fontSizeSmall\":\"Klein\",\"fontSizeMedium\":\"Gemiddeld\",\"fontSizeLarge\":\"Groot\",\"fontSizeDescription\":\"Pas de tekstgrootte in de hele applicatie aan.\",\"highContrastModeLabel\":\"Hoge Contrastmodus\",\"highContrastModeDescription\":\"Verhoogt het tekst/achtergrondcontrast voor betere leesbaarheid.\",\"enabledLabel\":\"Ingeschakeld\",\"disabledLabel\":\"Uitgeschakeld\",\"personalDictionaryLabel\":\"Persoonlijk Woordenboek\",\"personalDictionaryDescription\":\"Voeg woorden toe die u vaak gebruikt en die mogelijk als fouten worden gemarkeerd.\",\"addWordPlaceholder\":\"Voer een woord in...\",\"addWordButton\":\"Woord Toevoegen\",\"deleteWordButtonAria\":\"Woord {{word}} verwijderen\",\"dictionaryEmptyPlaceholder\":\"Uw woordenboek is leeg. Voeg wat woorden toe!\",\"dictionaryImportExportLabel\":\"Woordenboek Importeren / Exporteren\",\"importDictionaryButton\":\"Importeren\",\"exportDictionaryButton\":\"Exporteren\",\"dictionaryImportExportDescription\":\"Maak een back-up van uw persoonlijke woordenboek of deel het als een JSON-bestand.\",\"clearDictionaryForLanguageButton\":\"Wis {{language}} Woordenboek\",\"clearDictionaryConfirmTitle\":\"Weet u het zeker?\",\"clearDictionaryForLanguageConfirmDescription\":\"Dit zal alle woorden permanent uit uw persoonlijke woordenboek voor {{language}} verwijderen. Deze actie kan niet ongedaan worden gemaakt.\",\"confirmClearButton\":\"Ja, Woordenboek Wissen\",\"clearDictionaryWarning\":\"Deze actie is onomkeerbaar.\",\"toastDictionaryWordAdded\":\"Woord '{{word}}' toegevoegd aan woordenboek.\",\"toastDictionaryWordExists\":\"Woord '{{word}}' bestaat al in woordenboek.\",\"toastDictionaryWordEmpty\":\"Kan geen leeg woord toevoegen.\",\"toastDictionaryWordDeleted\":\"Woord '{{word}}' verwijderd uit woordenboek.\",\"toastDictionaryImportOverwriteSuccess\":\"{{count}} woorden geïmporteerd, woordenboek overschreven.\",\"toastDictionaryImportMergeSuccess\":\"{{count}} nieuwe woorden geïmporteerd en samengevoegd.\",\"toastDictionaryImportInvalidFormat\":\"Ongeldig woordenboekbestandsformaat. Moet een JSON-array van strings zijn.\",\"toastDictionaryImportError\":\"Fout bij importeren woordenboekbestand.\",\"toastDictionaryExportSuccess\":\"Woordenboek succesvol geëxporteerd.\",\"toastDictionaryCleared\":\"Persoonlijk woordenboek gewist.\",\"toastLanguageSwitched\":\"Schrijftaal automatisch overgeschakeld naar {{language}}.\",\"writingAssistanceTitle\":\"Schrijfondersteuning\",\"writingAssistanceDescription\":\"Verbeter uw schrijfervaring met LinguaFlow door aan te passen hoe het u helpt.\",\"yourLanguageProficiencyTitle\":\"Uw Taalvaardigheid (voor Primaire Taal)\",\"yourLanguageProficiencyDescription\":\"Ontvang nauwkeurig afgestemde suggesties die aansluiten bij uw geavanceerde taalbegrip, wat zorgt voor een genuanceerdere benadering van uw schrijven.\",\"toneDetectionTitle\":\"Toondetectie\",\"toneDetectionDescription\":\"Duik in de emotionele ondertonen van uw tekst met onze toondetectiefunctie, die uw schrijven analyseert en inzichtelijke suggesties biedt om uw toon te verfijnen en te verheffen, zodat deze effectiever resoneert met uw publiek.\",\"nonNativeSupportTitle\":\"Ondersteuning voor niet-moedertaalsprekers\",\"nonNativeSupportDescription\":\"Profiteer van gespecialiseerde hulp gericht op niet-moedertaalsprekers, met doordachte begeleiding en praktische tips om uw spreekvaardigheid en zelfvertrouwen bij het schrijven te verbeteren.\",\"advancedSettingsTitle\":\"Geavanceerde Instellingen\",\"enableOfflineFunctionalityLabel\":\"Offline Functionaliteit Inschakelen\",\"enableOfflineFunctionalityDescription\":\"Basisfuncties, inclusief instellingen en het woordenboek, zijn offline beschikbaar. Belangrijke componenten worden toegevoegd om een naadloze werking van het programma te garanderen.\",\"enableAutomaticLanguageDetectionLabel\":\"Automatische Taalherkenning Inschakelen\",\"enableAutomaticLanguageDetectionDescription\":\"Detecteer en schakel automatisch de schrijftaal om terwijl u typt.\",\"dataManagementLabel\":\"Gegevensbeheer\",\"resetAllSettingsLabel\":\"Alle Instellingen Resetten\",\"resetAllSettingsDescription\":\"Dit zal alle aanpassingen, inclusief thema, taal en functie-instellingen, terugzetten naar hun standaardwaarden. Deze actie kan niet ongedaan worden gemaakt.\",\"resetButtonLabel\":\"Resetten\",\"resetAllSettingsConfirmTitle\":\"Weet u zeker dat u alle instellingen wilt resetten?\",\"resetAllSettingsConfirmDescription\":\"Al uw persoonlijke instellingen, woordenboekwoorden en voorkeuren worden permanent verwijderd en teruggezet naar de standaardinstellingen van de applicatie. Deze actie kan niet ongedaan worden gemaakt.\",\"confirmResetButton\":\"Ja, Alles Resetten\",\"toastResetSuccess\":\"Alle instellingen zijn teruggezet naar de standaardwaarden. De applicatie wordt nu opnieuw geladen.\",\"dictionaryLanguageLabel\":\"Woordenboektaal\",\"selectDictionaryLanguagePlaceholder\":\"Selecteer taal om te bekijken...\",\"dictionaryLanguageDescription\":\"Bekijk en beheer het woordenboek voor een specifieke taal.\",\"toastSuggestionDismissed\":\"Suggestie genegeerd.\",\"dismissButton\":\"Negeren\",\"correctButton\":\"Corrigeren\",\"undoButton\":\"Ongedaan maken\",\"redoButton\":\"Opnieuw doen\",\"aiToolsButton\":\"Slimme Synoniemen\",\"wordToolkitTitle\":\"Synoniemen Suggestie\",\"wordToolkitDescription\":\"Selecteer een enkel woord in de editor om synoniemen te krijgen en de uitspraak te horen.\",\"wordToolkitPlaceholder\":\"Selecteer een enkel woord in de hoofdeditor om slimme synoniemen te krijgen.\",\"selectedWordLabel\":\"Geselecteerd Woord\",\"synonymsLabel\":\"Synoniemen\",\"noSynonymsFound\":\"Geen synoniemen gevonden.\",\"applySynonymTooltip\":\"Vervang woord door '{{synonym}}'\",\"toastWordToolkitError\":\"Kon geen suggesties voor het geselecteerde woord ophalen.\",\"toastWordReplacedSuccess\":\"Woord vervangen door '{{word}}'.\",\"wordToolkitPopoverDescription\":\"Krijg synoniemen of hoor de uitspraak van het woord.\",\"spellingAndPronunciationLabel\":\"Uitspraak\",\"pronounceButton\":\"Spreek Woord Uit\",\"toastPronunciationError\":\"Kon audio-uitspraak niet genereren.\",\"toastEmptyText\":\"Voer eerst wat tekst in.\"}"));}}),
"[project]/src/locales/it.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"appName\":\"LinguaFlow\",\"appDescription\":\"Assistente per la correzione grammaticale e la scrittura\",\"editorTitle\":\"Editor\",\"rephraseSelectionButton\":\"Riformula Selezione\",\"writingStatsTitle\":\"Statistiche di Scrittura\",\"wordCountLabel\":\"Conteggio Parole\",\"charCountLabel\":\"Conteggio Caratteri\",\"writingScoreLabel\":\"Punteggio di Scrittura\",\"writingScoreUnit\":\"/ 100\",\"writingModeLabel\":\"Modalità di Scrittura\",\"selectWritingModePlaceholder\":\"Seleziona modalità di scrittura\",\"formalWritingMode\":\"Formale\",\"casualWritingMode\":\"Casuale\",\"professionalWritingMode\":\"Professionale\",\"creativeWritingMode\":\"Creativo\",\"technicalWritingMode\":\"Tecnico\",\"academicWritingMode\":\"Accademico\",\"businessWritingMode\":\"Affari\",\"aiToneAnalysisAccordionTitle\":\"Analisi Tono\",\"aiToneAnalysisTitle\":\"Analisi Tono\",\"aiToneAnalysisDescription\":\"Ottieni feedback sulla formalità e sicurezza della tua scrittura.\",\"analyzeToneButton\":\"Analizza Tono del Testo\",\"formalityLabel\":\"Formalità\",\"confidenceLabel\":\"Sicurezza\",\"feedbackLabel\":\"Feedback\",\"writeSomeTextToAnalyzePlaceholder\":\"Scrivi del testo nell'editor per analizzarne il tono.\",\"aiTextGenerationAccordionTitle\":\"Generazione Contenuto AI\",\"aiTextGenerationTitle\":\"Generatore Contenuto AI\",\"aiTextGenerationDescription\":\"Genera contenuto basato sul tuo prompt.\",\"yourPromptLabel\":\"Il Tuo Prompt\",\"promptPlaceholder\":\"es. Scrivi una breve storia su un robot che scopre la musica\",\"generatedTextLabel\":\"Testo Generato\",\"generateTextButton\":\"Genera Testo\",\"settingsAccordionTitle\":\"Impostazioni\",\"settingsTitle\":\"Impostazioni\",\"settingsDescription\":\"Personalizza la tua esperienza LinguaFlow.\",\"themeLabel\":\"Tema\",\"switchToLightMode\":\"Passa a Modalità Chiara\",\"switchToDarkMode\":\"Passa a Modalità Scura\",\"languageLabel\":\"Lingua\",\"selectLanguagePlaceholder\":\"Seleziona lingua\",\"englishUSLanguage\":\"Inglese (Stati Uniti)\",\"englishUKLanguage\":\"Inglese (Regno Unito)\",\"arabicLanguage\":\"العربية (Arabic)\",\"turkishLanguage\":\"Türkçe (Turkish)\",\"spanishLanguage\":\"Español (Spanish)\",\"germanLanguage\":\"Deutsch (German)\",\"frenchLanguage\":\"Français (French)\",\"dutchLanguage\":\"Nederlands (Dutch)\",\"italianLanguage\":\"Italiano (Italian)\",\"startWritingPlaceholder\":\"Inizia a scrivere qui...\",\"rephrasePopoverTitle\":\"Riformula Testo\",\"rephrasePopoverDescription\":\"Rivedi il suggerimento per il testo selezionato.\",\"originalTextLabel\":\"Originale\",\"suggestionTextLabel\":\"Suggerimento\",\"rephraseWaitMessage\":\"Clicca \\\"Riformula\\\" o attendi il suggerimento.\",\"applyButton\":\"Applica\",\"cancelButton\":\"Annulla\",\"siteHeaderTitle\":\"LinguaFlow\",\"footerText\":\"Creato da Eng: AZA7© 2025. Tutti i diritti riservati®. Il vostro supporto e riconoscimento sono molto apprezzati.\",\"toastInputRequiredTitle\":\"Input Richiesto\",\"toastEditorEmptyError\":\"L'editor è vuoto. Scrivi del testo da analizzare.\",\"toastPromptRequiredError\":\"Inserisci un prompt per generare testo.\",\"toastSuccessTitle\":\"Successo\",\"toastErrorTitle\":\"Errore\",\"toastInfoTitle\":\"Info\",\"toastTextGeneratedSuccess\":\"Testo generato con successo.\",\"toastTextGenerationError\":\"Generazione testo fallita. Riprova.\",\"toastToneAnalysisSuccess\":\"Analisi tono completata.\",\"toastToneAnalysisError\":\"Analisi tono fallita. Riprova.\",\"toastNothingToRephraseError\":\"Niente da riformulare\",\"toastSelectTextToRephraseError\":\"Seleziona del testo nell'editor.\",\"toastSuggestionReady\":\"Suggerimento pronto\",\"toastRephraseError\":\"Riformulazione testo fallita. Riprova.\",\"toastFileUploadedSuccess\":\"Contenuto del file caricato nell'editor.\",\"toastFileTypeNotSupportedError\":\"Tipo di file non supportato. Carica un file {{fileType}}.\",\"plagiarismDetectionAccordionTitle\":\"Rilevamento Plagio\",\"plagiarismDetectionTitle\":\"Rilevamento Plagio\",\"plagiarismDetectionSettingsTitle\":\"Rilevamento Plagio\",\"plagiarismDetectionDescription\":\"Proteggi la tua integrità utilizzando il nostro strumento di rilevamento del plagio, progettato per scansionare meticolosamente i tuoi contenuti alla ricerca di somiglianze non intenzionali con la letteratura esistente, aiutandoti a mantenere l'originalità nella tua scrittura.\",\"detectPlagiarismButton\":\"Rileva Plagio nel Testo\",\"originalityScoreLabel\":\"Punteggio di Originalità\",\"plagiarismReportLabel\":\"Rapporto di Analisi\",\"potentialSourcesFoundLabel\":\"Potenziali Fonti Trovate\",\"originalSourceLabel\":\"Fonte Originale\",\"similarityScoreLabel\":\"Punteggio di Somiglianza\",\"toastPlagiarismDetectionSuccess\":\"Controllo plagio completato.\",\"toastPlagiarismDetectionError\":\"Controllo plagio fallito. Riprova.\",\"writeSomeTextToDetectPlagiarismPlaceholder\":\"Scrivi del testo nell'editor per controllare il plagio.\",\"aiWritingDetectionAccordionTitle\":\"Rilevamento Scrittura AI\",\"aiWritingDetectionTitle\":\"Rilevamento Scrittura AI\",\"aiWritingDetectionDescription\":\"Stima la probabilità che il tuo testo sia stato generato dall'IA.\",\"detectAiWritingButton\":\"Rileva Scrittura AI nel Testo\",\"probabilityAIWrittenLabel\":\"Probabilità Scritto da IA\",\"aiWritingDetectionSummaryLabel\":\"Riepilogo Analisi\",\"toastAiWritingDetectionSuccess\":\"Rilevamento scrittura AI completato.\",\"toastAiWritingDetectionError\":\"Rilevamento scrittura AI fallito. Riprova.\",\"writeSomeTextToDetectAiWritingPlaceholder\":\"Scrivi del testo nell'editor per controllare l'autorialità IA.\",\"writingSuggestionsTitle\":\"Suggerimenti di Scrittura\",\"analyzingTextDescription\":\"L'IA sta analizzando il tuo testo per suggerimenti...\",\"suggestionsFoundDescription\":\"{{count}} suggerimenti trovati. Esaminali qui sotto.\",\"noSuggestionsFoundDescription\":\"Nessun suggerimento immediato trovato. Continua a scrivere o prova a riformulare.\",\"startTypingForSuggestionsDescription\":\"Inizia a digitare per suggerimenti di scrittura basati sull'IA.\",\"suggestionTypeSpelling\":\"Ortografia\",\"suggestionTypeGrammar\":\"Grammatica\",\"suggestionTypeRewrite\":\"Riscrivi\",\"suggestionTypeStyle\":\"Stile\",\"suggestionTypeUnknown\":\"Suggerimento\",\"suggestionLabel\":\"Suggerisce\",\"applySuggestionButton\":\"Applica\",\"suggestionExplanationTooltip\":\"Visualizza spiegazione\",\"toastTextAnalysisError\":\"Impossibile ottenere suggerimenti di scrittura. Riprova.\",\"toastSuggestionAppliedSuccess\":\"Suggerimento applicato.\",\"toastSuggestionApplyError\":\"Impossibile applicare il suggerimento. Il testo originale potrebbe essere cambiato.\",\"humanizeAiTextAccordionTitle\":\"Umanizza Testo AI\",\"humanizeAiTextTitle\":\"Umanizza Testo AI\",\"humanizeAiTextDescription\":\"Riscrivi il testo generato dall'IA per farlo sembrare più umano.\",\"humanizeTextButton\":\"Umanizza Testo\",\"humanizedTextLabel\":\"Testo Umanizzato\",\"toastHumanizeTextSuccess\":\"Testo umanizzato con successo.\",\"toastHumanizeTextError\":\"Umanizzazione testo fallita. Riprova.\",\"writeSomeTextToHumanizePlaceholder\":\"Scrivi o incolla testo generato dall'IA nell'editor per umanizzarlo.\",\"clearEditorButton\":\"Cancella\",\"clearEditorButtonAriaLabel\":\"Cancella tutto il testo dall'editor\",\"toastEditorClearedSuccess\":\"Contenuto dell'editor cancellato.\",\"generationHistoryTitle\":\"Cronologia Generazioni\",\"noGenerationsYetPlaceholder\":\"Nessuna generazione ancora. Genera del testo per vederlo qui.\",\"promptLabel\":\"Prompt\",\"outputLabel\":\"Output\",\"useThisPromptButton\":\"Usa questo Prompt\",\"copyOutputButton\":\"Copia Output\",\"toastPromptRestoredSuccess\":\"Prompt ripristinato nel campo di input.\",\"toastTextCopiedSuccess\":\"Testo copiato negli appunti.\",\"toastTextCopyError\":\"Impossibile copiare il testo negli appunti.\",\"insertIntoEditorButton\":\"Inserisci nell'Editor\",\"insertIntoEditorButtonTooltip\":\"Aggiungi testo generato all'editor\",\"toastTextInsertedSuccess\":\"Testo generato inserito nell'editor.\",\"copyEditorButton\":\"Copia Testo\",\"copyEditorButtonAriaLabel\":\"Copia tutto il testo dall'editor\",\"toastEditorContentCopiedSuccess\":\"Contenuto dell'editor copiato negli appunti.\",\"toastEditorContentCopyError\":\"Impossibile copiare il contenuto dell'editor negli appunti.\",\"toastEditorEmptyForCopyError\":\"L'editor è vuoto. Niente da copiare.\",\"recordVoiceButtonStart\":\"Registra Voce\",\"recordVoiceButtonStop\":\"Ferma Registrazione\",\"recordVoiceButtonAriaLabelStart\":\"Avvia registrazione vocale per trascrivere in testo\",\"recordVoiceButtonAriaLabelStop\":\"Ferma registrazione vocale\",\"toastRecordingStarted\":\"Registrazione avviata. Parla nel microfono.\",\"toastRecordingStoppedNoTranscript\":\"Registrazione interrotta. Nessun discorso è stato trascritto.\",\"toastSpeechTranscribedAndAppended\":\"Discorso trascritto e aggiunto all'editor.\",\"toastSpeechRecognitionNotSupported\":\"Il riconoscimento vocale non è supportato dal tuo browser.\",\"toastMicrophonePermissionDenied\":\"Permesso microfono negato. Abilitalo nelle impostazioni del browser e aggiorna la pagina.\",\"toastSpeechNoSpeechDetected\":\"Nessun discorso rilevato. Riprova.\",\"toastSpeechAudioCaptureError\":\"Errore cattura audio. Controlla il microfono.\",\"toastSpeechNetworkError\":\"Errore di rete durante il riconoscimento vocale. Controlla la connessione.\",\"toastSpeechRecognitionError\":\"Errore riconoscimento vocale: {{error}}\",\"toastSpeechServiceNotAllowed\":\"Il servizio di riconoscimento vocale non è consentito o non è disponibile. Riprova più tardi.\",\"toastSpeechLanguageNotSupportedError\":\"La lingua selezionata non è supportata per il riconoscimento vocale dal tuo browser.\",\"helpTitle\":\"Aiuto\",\"helpPanelTitle\":\"Come usare LinguaFlow\",\"helpPanelDescription\":\"Inizia con le funzionalità di LinguaFlow.\",\"helpPanelIntro\":\"Benvenuto in LinguaFlow! Questa guida ti aiuterà a navigare e utilizzare i potenti strumenti di assistenza alla scrittura dell'applicazione.\",\"helpEditorTitle\":\"L'Editor\",\"helpAiToolsTitle\":\"Pannello Strumenti AI\",\"helpLanguageSettingsTitle\":\"Impostazioni della lingua\",\"helpAppearanceSettingsTitle\":\"Impostazioni di aspetto\",\"helpDictionarySettingsTitle\":\"Impostazioni del dizionario\",\"helpFeatureSettingsTitle\":\"Impostazioni delle funzionalità\",\"helpWritingAidSettingsTitle\":\"Impostazioni di aiuto alla scrittura\",\"helpAdvancedSettingsTitle\":\"Impostazioni avanzate\",\"helpEditorDescription\":\"L'Editor è il tuo spazio di lavoro principale.<br/><br/><b>- Suggerimenti in tempo reale:</b> Mentre digiti, l'app controlla automaticamente il testo e sottolinea i potenziali problemi. Clicca su un segmento evidenziato per vedere un popover di correzione.<br/><b>- Strumenti AI sulla selezione:</b> Seleziona una parte di testo per far apparire il pulsante <b>Sinonimi Intelligenti</b>. Se selezioni una sola parola, otterrai sinonimi e una guida alla pronuncia. Se selezioni una frase più lunga, otterrai un suggerimento di riformulazione basato sull'IA.<br/><b>- Barra degli strumenti di formattazione:</b> In cima all'editor, troverai strumenti per Annulla/Ripristina, cambiare i caratteri e applicare formattazioni come Grassetto, Corsivo, elenchi e allineamento del testo.\",\"helpAiToolsDescription\":\"I pannelli a sinistra e a destra forniscono potenti capacità di IA.<br/><br/><b>- Strumenti di scrittura (Sinistra):</b> Qui puoi cambiare la <b>Modalità di Scrittura</b> per influenzare lo stile dell'IA, <b>Importare un Documento</b>, o usare il <b>Riscrivitore IA</b> per riformulare l'intero testo. Puoi anche usare strumenti per <b>Umanizzare il Testo IA</b>, controllare la <b>Scrittura IA</b>, e rilevare il <b>Plagio</b>.<br/><b>- Strumenti di analisi (Destra):</b> Questa colonna mostra <b>Statistiche di Scrittura</b> in tempo reale, fornisce un <b>Analizzatore di Tono</b> per il tuo testo, e include il <b>Generatore di Contenuto IA</b> per creare nuovo testo da un prompt. La cronologia delle tue generazioni viene salvata qui per un facile riutilizzo.\",\"helpLanguageSettingsDescription\":\"Configura la <b>Lingua dell'Interfaccia Utente</b> per l'interfaccia dell'applicazione e la <b>Lingua di Scrittura Principale</b> per l'analisi. Per alcune lingue, puoi anche selezionare un <b>Dialetto Regionale</b>. Abilita il <b>Rilevamento Automatico della Lingua</b> per far sì che l'app cambi la lingua di scrittura mentre digiti.\",\"helpAppearanceSettingsDescription\":\"Personalizza l'aspetto dell'app. Scegli un <b>Tema</b> (Chiaro, Scuro o di Sistema), regola la <b>Dimensione del Carattere</b> globale e attiva la <b>Modalità ad Alto Contrasto</b> per una migliore leggibilità.\",\"helpDictionarySettingsDescription\":\"Gestisci i tuoi dizionari personali per diverse lingue. <b>Aggiungi</b> parole che usi spesso ma che potrebbero essere segnalate come errori di ortografia (come nomi o gergo tecnico). Puoi anche <b>Importare</b> o <b>Esportare</b> la tua lista del dizionario come file JSON, o <b>Cancellare</b> il dizionario per una lingua specifica.\",\"helpFeatureSettingsDescription\":\"Affina il comportamento di specifiche funzionalità di IA e di correzione automatica. Qui puoi attivare/disattivare vari strumenti di IA generativa, comportamenti di correzione automatica e le funzionalità di controllo in tempo reale principali per adattarle al tuo flusso di lavoro.\",\"helpWritingAidSettingsDescription\":\"Personalizza il modo in cui l'IA ti assiste. Imposta la tua <b>Competenza Linguistica</b> per ottenere suggerimenti su misura per il tuo livello di abilità. Puoi anche abilitare o disabilitare funzionalità come il <b>Rilevamento del Tono</b>, il <b>Rilevamento del Plagio</b> e il supporto specializzato per i <b>Non Madrelingua</b>.\",\"helpAdvancedSettingsDescription\":\"Controlla i comportamenti operativi principali. Abilita la <b>Funzionalità Offline</b> per usare le funzionalità di base senza una connessione internet. Se vuoi ricominciare da capo, puoi <b>Reimpostare Tutte le Impostazioni</b> per ripristinare l'applicazione alle sue impostazioni predefinite originali (questa azione non può essere annullata).\",\"helpPanelTip\":\"Sperimenta con diversi strumenti e impostazioni per trovare ciò che funziona meglio per il tuo stile di scrittura e le tue esigenze!\",\"Write Tools\":\"Strumenti di Scrittura\",\"Import Document\":\"Importa Documento\",\"Quick Action\":\"Azione Rapida\",\"Tone Analyzer\":\"Analizzatore di Tono\",\"aiRewriteAccordionTitle\":\"Riscrivitore IA\",\"aiRewriteTitle\":\"Riscrivitore IA\",\"aiRewriteDescription\":\"Riscrivi l'intero contenuto dell'editor per migliorare chiarezza e stile.\",\"rewriteEditorContentButton\":\"Riscrivi Contenuto Editor\",\"rewrittenTextLabel\":\"Testo Riscritto\",\"applyToEditorButton\":\"Applica all'Editor\",\"toastRewriteSuccess\":\"Contenuto dell'editor riscritto con successo.\",\"toastRewriteError\":\"Impossibile riscrivere il contenuto dell'editor. Riprova.\",\"writeSomeTextToRewritePlaceholder\":\"Scrivi del testo nell'editor per riscriverlo.\",\"Click the button to rewrite the editor content.\":\"Clicca sul pulsante per riscrivere il contenuto dell'editor.\",\"dropzoneInstruction\":\"Rilascia i file qui o sfoglia\",\"toastFileImportSuccessTitle\":\"File Importato\",\"toastFileImportSuccessMessage\":\"Contenuto del documento caricato.\",\"toastFileImportErrorTitle\":\"Errore di Importazione\",\"toastFileImportErrorMessage\":\"Impossibile leggere il contenuto del file. Assicurati che sia un file .txt valido.\",\"toastInvalidFileTypeMessage\":\"Tipo di file non valido. Sono accettati solo file .txt.\",\"dropzoneAriaLabel\":\"Area di rilascio per importazione documenti: Clicca o trascina e rilascia un file .txt per caricarlo.\",\"featuresLabel\":\"Funzionalità\",\"featureSettingsDescription\":\"Personalizza la funzionalità delle specifiche funzionalità di assistenza alla scrittura.\",\"appearanceLabel\":\"Aspetto\",\"writingAidLabel\":\"Aiuto Scrittura\",\"dictionaryLabel\":\"Dizionario\",\"dictionarySettingsDescription\":\"Aggiungi parole personalizzate o gestisci dizionari personali. (Segnaposto)\",\"advancedSettingsLabel\":\"Avanzate\",\"advancedSettingsDescription\":\"Accedi alle opzioni di configurazione avanzate. Usa con cautela. (Segnaposto)\",\"uiLanguageLabel\":\"Lingua Interfaccia Utente\",\"selectUiLanguagePlaceholder\":\"Seleziona lingua UI...\",\"uiLanguageDescription\":\"Cambia la lingua dell'interfaccia dell'applicazione.\",\"writingLanguageLabel\":\"Lingua di Scrittura Principale\",\"selectWritingLanguagePlaceholder\":\"Seleziona lingua di scrittura...\",\"writingLanguageDescription\":\"Imposta la lingua principale per l'analisi e la generazione AI.\",\"regionalDialectLabel\":\"Dialetto Regionale\",\"selectRegionalDialectPlaceholder\":\"Seleziona dialetto...\",\"regionalDialectDescription\":\"Specifica la variazione regionale per la lingua di scrittura selezionata.\",\"languageProficiencyLabel\":\"Competenza Linguistica\",\"selectProficiencyPlaceholder\":\"Seleziona competenza...\",\"languageProficiencyDescription\":\"Aiuta l'IA ad adattare i suggerimenti al tuo livello di competenza linguistica.\",\"proficiencyNative\":\"Nativo\",\"proficiencyAdvanced\":\"Avanzato (C1/C2)\",\"proficiencyIntermediate\":\"Intermedio (B1/B2)\",\"proficiencyBeginner\":\"Principiante (A1/A2)\",\"languageEnglishGeneral\":\"Inglese\",\"languageSpanishGeneral\":\"Spagnolo\",\"languageFrenchGeneral\":\"Francese\",\"languageGermanGeneral\":\"Tedesco\",\"languageItalianGeneral\":\"Italiano\",\"languageDutchGeneral\":\"Olandese\",\"languageArabicGeneral\":\"Arabo\",\"arabicSyriaLanguage\":\"Arabo (Siria)\",\"arabicSaudiArabiaLanguage\":\"Arabo (Arabia Saudita)\",\"arabicEgyptLanguage\":\"Arabo (Egitto)\",\"languageTurkishGeneral\":\"Turco\",\"spanishSpainLanguage\":\"Spagnolo (Spagna)\",\"spanishMexicoLanguage\":\"Spagnolo (Messico)\",\"themeLight\":\"Chiaro\",\"themeDark\":\"Scuro\",\"themeSystem\":\"Predefinito di Sistema\",\"selectThemePlaceholder\":\"Seleziona tema...\",\"themeDescription\":\"Scegli il tema visuale dell'applicazione.\",\"fontSizeLabel\":\"Dimensione Carattere\",\"selectFontSizePlaceholder\":\"Seleziona dimensione carattere...\",\"fontSizeSmall\":\"Piccolo\",\"fontSizeMedium\":\"Medio\",\"fontSizeLarge\":\"Grande\",\"fontSizeDescription\":\"Regola la dimensione del testo in tutta l'applicazione.\",\"highContrastModeLabel\":\"Modalità Alto Contrasto\",\"highContrastModeDescription\":\"Aumenta il contrasto testo/sfondo per una migliore leggibilità.\",\"enabledLabel\":\"Abilitato\",\"disabledLabel\":\"Disabilitato\",\"personalDictionaryLabel\":\"Dizionario Personale\",\"personalDictionaryDescription\":\"Aggiungi parole che usi frequentemente e che potrebbero essere segnalate come errori.\",\"addWordPlaceholder\":\"Inserisci una parola...\",\"addWordButton\":\"Aggiungi Parola\",\"deleteWordButtonAria\":\"Elimina parola {{word}}\",\"dictionaryEmptyPlaceholder\":\"Il tuo dizionario è vuoto. Aggiungi qualche parola!\",\"dictionaryImportExportLabel\":\"Importa / Esporta Dizionario\",\"importDictionaryButton\":\"Importa\",\"exportDictionaryButton\":\"Esporta\",\"dictionaryImportExportDescription\":\"Esegui il backup o condividi il tuo dizionario personale come file JSON.\",\"clearDictionaryForLanguageButton\":\"Cancella Dizionario {{language}}\",\"clearDictionaryConfirmTitle\":\"Sei sicuro?\",\"clearDictionaryForLanguageConfirmDescription\":\"Questo eliminerà permanentemente tutte le parole dal tuo dizionario personale per {{language}}. Questa azione non può essere annullata.\",\"confirmClearButton\":\"Sì, Cancella Dizionario\",\"clearDictionaryWarning\":\"Questa azione è irreversibile.\",\"toastDictionaryWordAdded\":\"Parola '{{word}}' aggiunta al dizionario.\",\"toastDictionaryWordExists\":\"La parola '{{word}}' esiste già nel dizionario.\",\"toastDictionaryWordEmpty\":\"Impossibile aggiungere una parola vuota.\",\"toastDictionaryWordDeleted\":\"Parola '{{word}}' eliminata dal dizionario.\",\"toastDictionaryImportOverwriteSuccess\":\"{{count}} parole importate, dizionario sovrascritto.\",\"toastDictionaryImportMergeSuccess\":\"{{count}} nuove parole importate e unite.\",\"toastDictionaryImportInvalidFormat\":\"Formato file dizionario non valido. Deve essere un array JSON di stringhe.\",\"toastDictionaryImportError\":\"Errore durante l'importazione del file dizionario.\",\"toastDictionaryExportSuccess\":\"Dizionario esportato con successo.\",\"toastDictionaryCleared\":\"Dizionario personale cancellato.\",\"toastLanguageSwitched\":\"Lingua di scrittura cambiata automaticamente in {{language}}.\",\"writingAssistanceTitle\":\"Assistenza alla Scrittura\",\"writingAssistanceDescription\":\"Migliora la tua esperienza di scrittura con LinguaFlow personalizzando come ti assiste.\",\"yourLanguageProficiencyTitle\":\"La Tua Competenza Linguistica (per la Lingua Primaria)\",\"yourLanguageProficiencyDescription\":\"Ricevi suggerimenti meticolosamente personalizzati che si allineano alla tua sofisticata comprensione della lingua, garantendo un approccio più sfumato alla tua scrittura.\",\"toneDetectionTitle\":\"Rilevamento del Tono\",\"toneDetectionDescription\":\"Approfondisci le sfumature emotive del tuo testo con la nostra funzione di rilevamento del tono, che analizza la tua scrittura e offre suggerimenti perspicaci per affinare ed elevare il tuo tono, rendendolo più efficace per il tuo pubblico.\",\"nonNativeSupportTitle\":\"Supporto per non Madrelingua\",\"nonNativeSupportDescription\":\"Approfitta di un'assistenza specializzata rivolta a chi non è madrelingua, fornendo una guida attenta e consigli pratici per migliorare la tua fluidità e sicurezza nella scrittura.\",\"advancedSettingsTitle\":\"Impostazioni Avanzate\",\"enableOfflineFunctionalityLabel\":\"Abilita Funzionalità Offline\",\"enableOfflineFunctionalityDescription\":\"Le funzionalità di base, incluse impostazioni e dizionario, sono disponibili offline. Verranno inclusi componenti chiave per garantire un funzionamento senza interruzioni del programma.\",\"enableAutomaticLanguageDetectionLabel\":\"Abilita Rilevamento Automatico Lingua\",\"enableAutomaticLanguageDetectionDescription\":\"Rileva e cambia automaticamente la lingua di scrittura mentre digiti.\",\"dataManagementLabel\":\"Gestione Dati\",\"resetAllSettingsLabel\":\"Reimposta Tutte le Impostazioni\",\"resetAllSettingsDescription\":\"Questo reimposterà tutte le personalizzazioni, inclusi tema, lingua e impostazioni delle funzionalità, ai loro valori predefiniti. Questa azione non può essere annullata.\",\"resetButtonLabel\":\"Reimposta\",\"resetAllSettingsConfirmTitle\":\"Sei sicuro di voler reimpostare tutte le impostazioni?\",\"resetAllSettingsConfirmDescription\":\"Tutte le tue impostazioni personali, le parole del dizionario e le preferenze verranno eliminate in modo permanente e ripristinate ai valori predefiniti dell'applicazione. Questa azione non può essere annullata.\",\"confirmResetButton\":\"Sì, Reimposta Tutto\",\"toastResetSuccess\":\"Tutte le impostazioni sono state ripristinate ai valori predefiniti. L'applicazione verrà ora ricaricata.\",\"dictionaryLanguageLabel\":\"Lingua del Dizionario\",\"selectDictionaryLanguagePlaceholder\":\"Seleziona lingua da visualizzare...\",\"dictionaryLanguageDescription\":\"Visualizza e gestisci il dizionario per una lingua specifica.\",\"toastSuggestionDismissed\":\"Suggerimento ignorato.\",\"dismissButton\":\"Ignora\",\"correctButton\":\"Correggi\",\"undoButton\":\"Annulla\",\"redoButton\":\"Ripristina\",\"aiToolsButton\":\"Sinonimi Intelligenti\",\"wordToolkitTitle\":\"Suggerimento Sinonimi\",\"wordToolkitDescription\":\"Seleziona una singola parola nell'editor per ottenere sinonimi e ascoltarne la pronuncia.\",\"wordToolkitPlaceholder\":\"Seleziona una singola parola nell'editor principale per ottenere sinonimi intelligenti.\",\"selectedWordLabel\":\"Parola selezionata\",\"synonymsLabel\":\"Sinonimi\",\"noSynonymsFound\":\"Nessun sinonimo trovato.\",\"applySynonymTooltip\":\"Sostituisci la parola con '{{synonym}}'\",\"toastWordToolkitError\":\"Impossibile ottenere suggerimenti per la parola selezionata.\",\"toastWordReplacedSuccess\":\"Parola sostituita con '{{word}}'.\",\"wordToolkitPopoverDescription\":\"Ottieni sinonimi o ascolta la pronuncia della parola.\",\"spellingAndPronunciationLabel\":\"Pronuncia\",\"pronounceButton\":\"Pronuncia Parola\",\"toastPronunciationError\":\"Impossibile generare la pronuncia audio.\",\"toastEmptyText\":\"Per favore, inserisci prima del testo.\"}"));}}),
"[project]/src/contexts/i18n-context.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "I18nProvider": (()=>I18nProvider),
    "useI18n": (()=>useI18n)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$languages$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/config/languages.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$en$2d$US$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/locales/en-US.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$en$2d$GB$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/locales/en-GB.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$ar$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/locales/ar.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$tr$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/locales/tr.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$es$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/locales/es.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$de$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/locales/de.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$fr$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/locales/fr.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$nl$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/locales/nl.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$it$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/locales/it.json (json)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
const getSafeTranslations = (json)=>{
    if (json && typeof json === 'object' && Object.keys(json).length > 0) {
        return json;
    }
    return {};
};
const allTranslationsData = {
    'en-US': getSafeTranslations(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$en$2d$US$2e$json__$28$json$29$__["default"]),
    'en-GB': getSafeTranslations(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$en$2d$GB$2e$json__$28$json$29$__["default"]),
    'ar': getSafeTranslations(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$ar$2e$json__$28$json$29$__["default"]),
    'tr': getSafeTranslations(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$tr$2e$json__$28$json$29$__["default"]),
    'es': getSafeTranslations(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$es$2e$json__$28$json$29$__["default"]),
    'de': getSafeTranslations(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$de$2e$json__$28$json$29$__["default"]),
    'fr': getSafeTranslations(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$fr$2e$json__$28$json$29$__["default"]),
    'nl': getSafeTranslations(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$nl$2e$json__$28$json$29$__["default"]),
    'it': getSafeTranslations(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$it$2e$json__$28$json$29$__["default"])
};
const DEFAULT_UI_LANGUAGE = 'en-US';
const DEFAULT_WRITING_LANGUAGE_DIALECT = 'en-US'; // Store full dialect
const DEFAULT_PROFICIENCY = 'native';
const I18nContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function I18nProvider({ children }) {
    _s();
    const [uiLanguage, setUiLanguageState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(DEFAULT_UI_LANGUAGE);
    const [translations, setTranslations] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(allTranslationsData[DEFAULT_UI_LANGUAGE]);
    const [writingLanguageDialect, setWritingLanguageDialectState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(DEFAULT_WRITING_LANGUAGE_DIALECT);
    const [languageProficiency, setLanguageProficiencyState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const updateDocumentAttributes = (langCode)=>{
        const langInfo = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$languages$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APP_SUPPORTED_UI_LANGUAGES"].find((l)=>l.value === langCode) || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$languages$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APP_SUPPORTED_UI_LANGUAGES"][0];
        document.documentElement.lang = langCode;
        document.documentElement.dir = langInfo?.dir || 'ltr';
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "I18nProvider.useEffect": ()=>{
            // Load UI Language from localStorage or browser settings
            const storedUiLanguage = localStorage.getItem('lingua-flow-ui-language');
            let initialLang = storedUiLanguage;
            if (!initialLang) {
                const browserLang = (navigator.languages ? navigator.languages[0] : navigator.language) || DEFAULT_UI_LANGUAGE;
                const matchingSupportedLang = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$languages$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APP_SUPPORTED_UI_LANGUAGES"].find({
                    "I18nProvider.useEffect.matchingSupportedLang": (lang)=>lang.value === browserLang || browserLang.startsWith(lang.value)
                }["I18nProvider.useEffect.matchingSupportedLang"]);
                initialLang = matchingSupportedLang ? matchingSupportedLang.value : DEFAULT_UI_LANGUAGE;
            }
            // Set initial language state
            setUiLanguage(initialLang);
            // Load Writing Language Dialect
            const storedWritingLangDialect = localStorage.getItem('lingua-flow-writing-language-dialect');
            setWritingLanguageDialectState(storedWritingLangDialect || DEFAULT_WRITING_LANGUAGE_DIALECT);
            // Load Language Proficiency
            const storedProficiency = localStorage.getItem('lingua-flow-language-proficiency');
            if (storedProficiency) {
                try {
                    setLanguageProficiencyState(JSON.parse(storedProficiency));
                } catch (e) {
                    localStorage.removeItem('lingua-flow-language-proficiency'); // Clear if invalid
                }
            }
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["I18nProvider.useEffect"], []);
    const setUiLanguage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "I18nProvider.useCallback[setUiLanguage]": (lang)=>{
            const validLang = allTranslationsData[lang] ? lang : DEFAULT_UI_LANGUAGE;
            setUiLanguageState(validLang);
            setTranslations(allTranslationsData[validLang]);
            localStorage.setItem('lingua-flow-ui-language', validLang);
            updateDocumentAttributes(validLang);
        }
    }["I18nProvider.useCallback[setUiLanguage]"], []);
    const setWritingLanguageDialect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "I18nProvider.useCallback[setWritingLanguageDialect]": (dialect)=>{
            setWritingLanguageDialectState(dialect);
            localStorage.setItem('lingua-flow-writing-language-dialect', dialect);
        }
    }["I18nProvider.useCallback[setWritingLanguageDialect]"], []);
    const getWritingLanguageBase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "I18nProvider.useCallback[getWritingLanguageBase]": ()=>{
            return writingLanguageDialect.split('-')[0];
        }
    }["I18nProvider.useCallback[getWritingLanguageBase]"], [
        writingLanguageDialect
    ]);
    const setLanguageProficiency = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "I18nProvider.useCallback[setLanguageProficiency]": (baseLang, level)=>{
            setLanguageProficiencyState({
                "I18nProvider.useCallback[setLanguageProficiency]": (prev)=>{
                    const newProficiency = {
                        ...prev,
                        [baseLang]: level
                    };
                    localStorage.setItem('lingua-flow-language-proficiency', JSON.stringify(newProficiency));
                    return newProficiency;
                }
            }["I18nProvider.useCallback[setLanguageProficiency]"]);
        }
    }["I18nProvider.useCallback[setLanguageProficiency]"], []);
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "I18nProvider.useCallback[t]": (key, params)=>{
            let mappedString = translations[key] !== undefined ? translations[key] : key;
            if (params) {
                Object.keys(params).forEach({
                    "I18nProvider.useCallback[t]": (paramKey)=>{
                        mappedString = mappedString.replace(new RegExp(`{{${paramKey}}}`, 'g'), String(params[paramKey]));
                    }
                }["I18nProvider.useCallback[t]"]);
            }
            return mappedString;
        }
    }["I18nProvider.useCallback[t]"], [
        translations
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(I18nContext.Provider, {
        value: {
            uiLanguage,
            setUiLanguage,
            writingLanguageDialect,
            setWritingLanguageDialect,
            getWritingLanguageBase,
            languageProficiency,
            setLanguageProficiency,
            t
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/i18n-context.tsx",
        lineNumber: 137,
        columnNumber: 5
    }, this);
}
_s(I18nProvider, "kFXNJTzzZi6renWOknmNwAXLmhA=");
_c = I18nProvider;
function useI18n() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(I18nContext);
    if (context === undefined) {
        throw new Error('useI18n must be used within an I18nProvider');
    }
    return context;
}
_s1(useI18n, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "I18nProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/theme-context.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AppearanceProvider": (()=>AppearanceProvider),
    "useAppearance": (()=>useAppearance)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
const AppearanceContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const FONT_SIZE_PX_MAP = {
    small: '14px',
    medium: '16px',
    large: '18px'
};
const DEFAULT_FONT_SIZE = 'medium';
const DEFAULT_THEME = 'system';
function AppearanceProvider({ children }) {
    _s();
    const [theme, setThemeState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(DEFAULT_THEME);
    const [fontSize, setFontSizeState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(DEFAULT_FONT_SIZE);
    const [isHighContrast, setIsHighContrastState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [effectiveTheme, setEffectiveTheme] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('light');
    const applyTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AppearanceProvider.useCallback[applyTheme]": (newTheme)=>{
            let currentEffectiveTheme;
            if (newTheme === 'system') {
                const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                currentEffectiveTheme = systemPrefersDark ? 'dark' : 'light';
            } else {
                currentEffectiveTheme = newTheme;
            }
            setEffectiveTheme(currentEffectiveTheme);
            document.documentElement.classList.remove('light', 'dark');
            document.documentElement.classList.add(currentEffectiveTheme);
            localStorage.setItem('lingua-flow-theme', newTheme);
            setThemeState(newTheme);
        }
    }["AppearanceProvider.useCallback[applyTheme]"], []);
    const applyFontSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AppearanceProvider.useCallback[applyFontSize]": (newSize)=>{
            document.documentElement.style.fontSize = FONT_SIZE_PX_MAP[newSize];
            localStorage.setItem('lingua-flow-font-size', newSize);
            setFontSizeState(newSize);
        }
    }["AppearanceProvider.useCallback[applyFontSize]"], []);
    const applyHighContrast = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AppearanceProvider.useCallback[applyHighContrast]": (newHighContrastState)=>{
            if (newHighContrastState) {
                document.documentElement.classList.add('high-contrast');
            } else {
                document.documentElement.classList.remove('high-contrast');
            }
            localStorage.setItem('lingua-flow-high-contrast', JSON.stringify(newHighContrastState));
            setIsHighContrastState(newHighContrastState);
        }
    }["AppearanceProvider.useCallback[applyHighContrast]"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AppearanceProvider.useEffect": ()=>{
            const storedTheme = localStorage.getItem('lingua-flow-theme');
            applyTheme(storedTheme || DEFAULT_THEME);
            const storedFontSize = localStorage.getItem('lingua-flow-font-size');
            applyFontSize(storedFontSize || DEFAULT_FONT_SIZE);
            const storedHighContrast = localStorage.getItem('lingua-flow-high-contrast');
            applyHighContrast(storedHighContrast ? JSON.parse(storedHighContrast) : false);
        }
    }["AppearanceProvider.useEffect"], [
        applyTheme,
        applyFontSize,
        applyHighContrast
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AppearanceProvider.useEffect": ()=>{
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            const handleChange = {
                "AppearanceProvider.useEffect.handleChange": ()=>{
                    if (theme === 'system') {
                        applyTheme('system'); // Re-apply to reflect system change
                    }
                }
            }["AppearanceProvider.useEffect.handleChange"];
            mediaQuery.addEventListener('change', handleChange);
            return ({
                "AppearanceProvider.useEffect": ()=>mediaQuery.removeEventListener('change', handleChange)
            })["AppearanceProvider.useEffect"];
        }
    }["AppearanceProvider.useEffect"], [
        theme,
        applyTheme
    ]);
    const setTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AppearanceProvider.useCallback[setTheme]": (newTheme)=>{
            applyTheme(newTheme);
        }
    }["AppearanceProvider.useCallback[setTheme]"], [
        applyTheme
    ]);
    const setFontSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AppearanceProvider.useCallback[setFontSize]": (newSize)=>{
            applyFontSize(newSize);
        }
    }["AppearanceProvider.useCallback[setFontSize]"], [
        applyFontSize
    ]);
    const toggleHighContrast = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AppearanceProvider.useCallback[toggleHighContrast]": ()=>{
            applyHighContrast(!isHighContrast);
        }
    }["AppearanceProvider.useCallback[toggleHighContrast]"], [
        isHighContrast,
        applyHighContrast
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AppearanceContext.Provider, {
        value: {
            theme,
            setTheme,
            fontSize,
            setFontSize,
            isHighContrast,
            toggleHighContrast,
            effectiveTheme
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/theme-context.tsx",
        lineNumber: 104,
        columnNumber: 5
    }, this);
}
_s(AppearanceProvider, "tjiTKAMZfYCuE58/tF3vClw+VV4=");
_c = AppearanceProvider;
function useAppearance() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AppearanceContext);
    if (context === undefined) {
        throw new Error('useAppearance must be used within an AppearanceProvider');
    }
    return context;
}
_s1(useAppearance, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "AppearanceProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_c7f26f5b._.js.map