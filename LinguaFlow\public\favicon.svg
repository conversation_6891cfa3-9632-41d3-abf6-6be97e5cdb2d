<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 
    FAVICON SVG TEMPLATE
    
    Replace this content with your logo SVG, optimized for small sizes (16x16 to 32x32)
    
    Tips for favicon SVGs:
    - Keep it simple - complex details won't show at small sizes
    - Use bold, contrasting colors
    - Avoid thin lines or small text
    - Test at 16x16 size to ensure visibility
  -->
  
  <defs>
    <linearGradient id="favicon-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#5DADE2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3498DB;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#favicon-gradient)" stroke="#2980B9" stroke-width="1"/>
  
  <!-- Placeholder content - replace with your logo elements -->
  <!-- Keep elements bold and simple for small sizes -->
  <text x="16" y="20" text-anchor="middle" fill="white" font-size="10" font-family="Arial, sans-serif" font-weight="bold">LF</text>
  
  <!-- 
    INSTRUCTIONS:
    1. Replace the content above with your logo SVG paths
    2. Ensure all elements are visible at 16x16 size
    3. Use the blue color scheme from your logo (#5DADE2, #3498DB)
    4. Keep stroke widths at least 1-2px for visibility
    5. Test the favicon in browser after replacement
  -->
</svg>
