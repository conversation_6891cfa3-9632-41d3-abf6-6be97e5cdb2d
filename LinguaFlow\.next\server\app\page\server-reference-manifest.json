{"node": {"40aafd7fc1c3a22f2c0d4374aea64f19bbb6f1a89a": {"workers": {"app/page": {"moduleId": "[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE6 => \"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE7 => \"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE8 => \"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE9 => \"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/page": "action-browser"}}, "40541a850961a912228a71046e05e9039bfe7c808c": {"workers": {"app/page": {"moduleId": "[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE6 => \"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE7 => \"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE8 => \"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE9 => \"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/page": "action-browser"}}, "404cdbee2139cd4a700c30a65f79bdc03aa46d6e86": {"workers": {"app/page": {"moduleId": "[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE6 => \"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE7 => \"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE8 => \"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE9 => \"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/page": "action-browser"}}, "4048b9b3d32fc6a8299fd3f3bdf7a06dd85439067c": {"workers": {"app/page": {"moduleId": "[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE6 => \"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE7 => \"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE8 => \"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE9 => \"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/page": "action-browser"}}, "40c2e1eb06515cf379e17d87e9f70ed83f12ecda4d": {"workers": {"app/page": {"moduleId": "[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE6 => \"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE7 => \"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE8 => \"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE9 => \"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/page": "action-browser"}}, "406af7a20cee789366903bd6ec9dcfde8b6b4e0966": {"workers": {"app/page": {"moduleId": "[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE6 => \"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE7 => \"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE8 => \"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE9 => \"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/page": "action-browser"}}, "40103b144fd3f8b981a27acf077ee8dc869c3f130c": {"workers": {"app/page": {"moduleId": "[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE6 => \"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE7 => \"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE8 => \"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE9 => \"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/page": "action-browser"}}, "4019ddd483a8143719b8581a53265f3ca17adb1e23": {"workers": {"app/page": {"moduleId": "[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE6 => \"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE7 => \"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE8 => \"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE9 => \"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/page": "action-browser"}}, "40a060d6732e9099e28cf4d8364b49c78cc62813a5": {"workers": {"app/page": {"moduleId": "[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE6 => \"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE7 => \"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE8 => \"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE9 => \"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/page": "action-browser"}}, "404e67fc06b869b875f3b5989fa3fa32f99b8c0471": {"workers": {"app/page": {"moduleId": "[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE6 => \"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE7 => \"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE8 => \"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE9 => \"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/page": "action-browser"}}}, "edge": {}}